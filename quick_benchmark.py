#!/usr/bin/env python3
"""
Quick LayerMatrix Performance Benchmark

Fast benchmark to estimate training time on Arkona data
before running the full training pipeline.
"""

import sys
import os
import numpy as np
import time
from sklearn.preprocessing import StandardScaler

# Import our modules
sys.path.insert(0, os.path.dirname(__file__))

import importlib.util
spec = importlib.util.spec_from_file_location("MassiveLayerMatrix", "lmn/MassiveLayerMatrix.py")
massive_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(massive_module)
MassiveLayerMatrix = massive_module.MassiveLayerMatrix

def run_quick_benchmark():
    """Run a quick benchmark to estimate training performance."""
    print("🏃‍♂️ LAYERMATRIX QUICK BENCHMARK")
    print("=" * 50)
    
    # Create synthetic data similar to Arkona dataset
    np.random.seed(42)
    
    # Simulate Arkona data dimensions
    n_samples = 1000  # Smaller subset for quick test
    n_features = 2048  # Same as processed Arkona data
    n_classes = 24     # Same as Arkona classes
    
    print(f"📊 Benchmark Dataset:")
    print(f"   Samples: {n_samples:,}")
    print(f"   Features: {n_features:,}")
    print(f"   Classes: {n_classes}")
    
    # Generate realistic data
    X = np.random.randn(n_samples, n_features).astype(np.float32)
    # Create some structure in the data
    for i in range(n_classes):
        class_mask = np.arange(n_samples) % n_classes == i
        X[class_mask, i*50:(i+1)*50] += 2.0  # Add class-specific signal
    
    y = np.arange(n_samples) % n_classes
    
    # Scale data
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    print(f"\n🧠 Testing LayerMatrix Architecture...")
    
    # Test different model sizes
    architectures = [
        ("Small", [512, 256]),
        ("Medium", [1024, 512, 256]),
        ("Large", [2048, 1024, 512, 256])
    ]
    
    results = []
    
    for name, hidden_layers in architectures:
        print(f"\n   Testing {name} model: {hidden_layers}")
        
        start_time = time.time()
        
        try:
            model = MassiveLayerMatrix(
                hidden_layers=hidden_layers,
                accumulation_ratio=0.15,
                learning_rate=1e-3,
                micro_batch_size=16,
                gradient_accumulation_steps=16,
                max_memory_gb=8.0,
                use_mixed_precision=True,
                adaptive_accumulation=True,
                layer_norm=True,
                dropout_rate=0.1,
                weight_decay=1e-5,
                warmup_steps=50
            )
            
            # Train for 3 epochs to get timing
            model.fit(X_scaled, y, epochs=3, verbose=False)
            
            # Test prediction
            predictions = model.predict(X_scaled[-200:])
            accuracy = np.mean(predictions == y[-200:])
            
            train_time = time.time() - start_time
            throughput = (n_samples * 3) / train_time  # samples per second
            params = model._estimate_parameters()
            
            result = {
                'name': name,
                'architecture': hidden_layers,
                'time': train_time,
                'accuracy': accuracy,
                'throughput': throughput,
                'parameters': params,
                'memory_estimate': params * 4 / (1024**3)  # GB estimate
            }
            
            results.append(result)
            
            print(f"      ✅ Time: {train_time:.2f}s")
            print(f"      ✅ Accuracy: {accuracy:.3f}")
            print(f"      ✅ Throughput: {throughput:.0f} samples/sec")
            print(f"      ✅ Parameters: {params:,}")
            
            model.cleanup()
            
        except Exception as e:
            print(f"      ❌ Failed: {e}")
            continue
    
    # Estimate full Arkona training time
    print(f"\n🎯 ARKONA TRAINING TIME ESTIMATES")
    print("=" * 50)
    
    arkona_samples = 9456
    arkona_epochs_cv = 3 * 10  # 3 folds × 10 epochs
    arkona_epochs_final = 15
    total_arkona_epochs = arkona_epochs_cv + arkona_epochs_final
    
    print(f"Arkona dataset: {arkona_samples:,} samples")
    print(f"Training plan: {arkona_epochs_cv} CV epochs + {arkona_epochs_final} final epochs")
    
    for result in results:
        if result['throughput'] > 0:
            # Scale up to full Arkona dataset
            scale_factor = arkona_samples / n_samples
            estimated_time_per_epoch = (arkona_samples / result['throughput'])
            total_estimated_time = estimated_time_per_epoch * total_arkona_epochs
            
            print(f"\n{result['name']} Model ({result['parameters']:,} params):")
            print(f"   Per epoch: {estimated_time_per_epoch:.1f}s")
            print(f"   Total training: {total_estimated_time:.1f}s ({total_estimated_time/60:.1f}m)")
            print(f"   Memory needed: {result['memory_estimate']:.1f}GB")
    
    # GPU utilization estimate
    print(f"\n🖥️  GPU UTILIZATION ESTIMATE")
    print("=" * 30)
    print(f"Vega 64 specs:")
    print(f"   Memory: 8GB HBM2")
    print(f"   Compute: 56 CUs, 4096 cores")
    print(f"   Memory bandwidth: 484 GB/s")
    
    if results:
        best_result = max(results, key=lambda x: x['throughput'])
        print(f"\nBest performance: {best_result['name']} model")
        print(f"   Throughput: {best_result['throughput']:.0f} samples/sec")
        print(f"   Memory usage: {best_result['memory_estimate']:.1f}/8.0 GB ({best_result['memory_estimate']/8*100:.1f}%)")
        
        # Estimate GPU utilization
        theoretical_peak = 13.8e12  # TFLOPS for Vega 64
        actual_ops = best_result['parameters'] * 2 * best_result['throughput']  # Rough estimate
        utilization = (actual_ops / theoretical_peak) * 100
        
        print(f"   Estimated GPU utilization: {min(utilization, 100):.1f}%")
    
    return results

def main():
    """Main benchmark function."""
    try:
        results = run_quick_benchmark()
        
        print(f"\n✅ Benchmark complete!")
        print(f"🚀 Ready to start full Arkona training with realistic time estimates.")
        
        return results
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    main()
