#!/usr/bin/env python3
"""Quick test of MassiveLayerMatrix"""

import sys
import os
import numpy as np

# Import the module
import importlib.util
spec = importlib.util.spec_from_file_location("MassiveLayerMatrix", "lmn/MassiveLayerMatrix.py")
massive_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(massive_module)
MassiveLayerMatrix = massive_module.MassiveLayerMatrix

def test_small_model():
    """Test a small model to verify the implementation works."""
    print("🚀 Testing MassiveLayerMatrix with small model...")
    
    # Create small dataset
    np.random.seed(42)
    X = np.random.randn(200, 32).astype(np.float32)
    y = (np.sum(X[:, :5], axis=1) > 0).astype(int)
    
    print(f"Dataset: {X.shape[0]} samples, {X.shape[1]} features")
    print(f"Target distribution: {np.mean(y):.3f}")
    
    # Create model
    model = MassiveLayerMatrix(
        hidden_layers=[64, 32],
        micro_batch_size=16,
        gradient_accumulation_steps=4,
        max_memory_gb=6.0,
        use_mixed_precision=False  # Disable for debugging
    )
    
    print(f"Model parameters: {model._estimate_parameters():,}")
    
    # Train
    print("🧠 Training...")
    model.fit(X, y, epochs=3, verbose=True)
    
    # Test prediction
    predictions = model.predict(X[-50:])
    accuracy = np.mean(predictions == y[-50:])
    print(f"🎯 Test accuracy: {accuracy:.3f}")
    
    model.cleanup()
    print("✅ Test completed successfully!")
    
    return accuracy

def test_larger_model():
    """Test a larger model to demonstrate scaling."""
    print("\n🚀 Testing larger MassiveLayerMatrix...")
    
    # Create larger dataset
    np.random.seed(42)
    X = np.random.randn(500, 128).astype(np.float32)
    y = (np.tanh(X @ np.random.randn(128) * 0.1) > 0).astype(int)
    
    print(f"Dataset: {X.shape[0]} samples, {X.shape[1]} features")
    
    # Create larger model
    model = MassiveLayerMatrix(
        hidden_layers=[256, 512, 256],
        micro_batch_size=8,
        gradient_accumulation_steps=16,
        max_memory_gb=6.0,
        use_mixed_precision=True
    )
    
    print(f"Model parameters: {model._estimate_parameters():,}")
    
    # Train
    print("🧠 Training larger model...")
    model.fit(X, y, epochs=2, verbose=True)
    
    # Test prediction
    predictions = model.predict(X[-100:])
    accuracy = np.mean(predictions == y[-100:])
    print(f"🎯 Test accuracy: {accuracy:.3f}")
    
    model.cleanup()
    print("✅ Larger test completed!")
    
    return accuracy

if __name__ == "__main__":
    print("🧪 MASSIVE LAYERMATRIX QUICK TESTS")
    print("=" * 50)
    
    try:
        acc1 = test_small_model()
        acc2 = test_larger_model()
        
        print(f"\n🏆 ALL TESTS PASSED!")
        print(f"Small model accuracy: {acc1:.3f}")
        print(f"Large model accuracy: {acc2:.3f}")
        print("✅ MassiveLayerMatrix is working correctly!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
