#!/usr/bin/env python3
"""
Arkona Data Processor for LayerMatrix Training

This script processes the rich Arkona dataset from multiple sources:
1. Corpus text data
2. Beast food JSON (emotion context)
3. Raw JSON files from various domains
4. Training data sources

Creates optimized training datasets for LayerMatrix to achieve high accuracy
on real-world tasks.
"""

import json
import os
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Any
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import LabelEncoder
import warnings
warnings.filterwarnings('ignore')

class ArkonaDataProcessor:
    """Process Arkona data for LayerMatrix training."""
    
    def __init__(self, base_path: str = "/Users/<USER>/Desktop/arkona_train/LMN_RELUKILLER/arkona_model"):
        self.base_path = base_path
        self.corpus_path = os.path.join(base_path, "corpus")
        self.training_data_path = os.path.join(base_path, "training_data")
        self.raw_json_path = os.path.join(base_path, "training_data/sources/raw_json")
        
        # Initialize text processing
        self.vectorizer = TfidfVectorizer(
            max_features=2048,  # Optimized for LayerMatrix
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.95
        )
        
        self.label_encoder = LabelEncoder()
        
        print("🚀 Arkona Data Processor initialized!")
        print(f"Base path: {base_path}")
    
    def load_corpus_data(self) -> List[str]:
        """Load the main corpus text data."""
        corpus_file = os.path.join(self.corpus_path, "arkona_knowledge_corpus.txt")
        
        if not os.path.exists(corpus_file):
            print(f"⚠️  Corpus file not found: {corpus_file}")
            return []
        
        with open(corpus_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Clean and filter lines
        corpus_data = []
        for line in lines:
            line = line.strip()
            if line and len(line) > 20:  # Filter out very short lines
                corpus_data.append(line)
        
        print(f"📚 Loaded {len(corpus_data):,} corpus entries")
        return corpus_data
    
    def load_beast_food_data(self) -> List[Dict]:
        """Load emotion context data from beast_food.json."""
        beast_file = os.path.join(self.training_data_path, "beast_food/beast_food.json")
        
        if not os.path.exists(beast_file):
            print(f"⚠️  Beast food file not found: {beast_file}")
            return []
        
        try:
            with open(beast_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            emotion_data = []
            if 'examples' in data and 'emotion_context' in data['examples']:
                for item in data['examples']['emotion_context']:
                    emotion_data.append({
                        'emotion': item.get('emotional_state', {}).get('label', 'neutral'),
                        'query': item.get('context_input', {}).get('query', ''),
                        'standard_response': item.get('standard_response', {}).get('content', ''),
                        'emotion_response': item.get('emotion_adapted_response', {}).get('content', ''),
                        'domain': 'emotion_context'
                    })
            
            print(f"😊 Loaded {len(emotion_data):,} emotion context entries")
            return emotion_data
            
        except Exception as e:
            print(f"❌ Error loading beast food data: {e}")
            return []
    
    def load_raw_json_data(self) -> List[Dict]:
        """Load all raw JSON files from various domains."""
        if not os.path.exists(self.raw_json_path):
            print(f"⚠️  Raw JSON path not found: {self.raw_json_path}")
            return []
        
        all_data = []
        domain_counts = {}
        
        # Walk through all subdirectories
        for root, dirs, files in os.walk(self.raw_json_path):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    domain = os.path.basename(root)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        # Process different JSON structures
                        if isinstance(data, list):
                            for item in data:
                                processed_item = self._process_json_item(item, domain, file)
                                if processed_item:
                                    all_data.append(processed_item)
                        elif isinstance(data, dict):
                            processed_item = self._process_json_item(data, domain, file)
                            if processed_item:
                                all_data.append(processed_item)
                        
                        domain_counts[domain] = domain_counts.get(domain, 0) + 1
                        
                    except Exception as e:
                        print(f"⚠️  Error loading {file_path}: {e}")
        
        print(f"🔬 Loaded {len(all_data):,} entries from {len(domain_counts)} domains:")
        for domain, count in sorted(domain_counts.items()):
            print(f"   {domain}: {count} files")
        
        return all_data
    
    def _process_json_item(self, item: Dict, domain: str, filename: str) -> Dict:
        """Process individual JSON items into standardized format."""
        processed = {
            'domain': domain,
            'source_file': filename,
            'text_content': '',
            'metadata': {}
        }
        
        # Extract text content from various fields
        text_parts = []
        
        # Common text fields
        for field in ['context', 'content', 'description', 'summary', 'text']:
            if field in item and isinstance(item[field], str):
                text_parts.append(item[field])
        
        # Handle investigation/learning structures
        if 'investigation' in item:
            inv = item['investigation']
            if 'hypothesis' in inv:
                text_parts.append(f"Hypothesis: {inv['hypothesis']}")
            if 'strategy' in inv:
                text_parts.append(f"Strategy: {inv['strategy']}")
            if 'actions' in inv and isinstance(inv['actions'], list):
                text_parts.extend(inv['actions'])
        
        if 'learning' in item:
            learn = item['learning']
            if 'new_knowledge' in learn:
                text_parts.append(f"Knowledge: {learn['new_knowledge']}")
            if 'follow_up_questions' in learn:
                for q in learn['follow_up_questions']:
                    if isinstance(q, dict) and 'answer' in q:
                        text_parts.append(q['answer'])
        
        # Handle gaps/topics
        if 'gaps' in item and isinstance(item['gaps'], list):
            for gap in item['gaps']:
                if isinstance(gap, dict) and 'topic' in gap:
                    text_parts.append(gap['topic'])
        
        # Combine all text
        processed['text_content'] = ' '.join(text_parts).strip()
        
        # Extract metadata
        for field in ['confidence', 'importance', 'score', 'id', 'index']:
            if field in item:
                processed['metadata'][field] = item[field]
        
        # Only return if we have meaningful text content
        if len(processed['text_content']) > 50:
            return processed
        
        return None
    
    def create_classification_dataset(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Create a classification dataset for LayerMatrix training."""
        print("\n🎯 Creating classification dataset...")
        
        # Load all data sources
        corpus_data = self.load_corpus_data()
        emotion_data = self.load_beast_food_data()
        json_data = self.load_raw_json_data()
        
        # Prepare text and labels
        texts = []
        labels = []
        
        # Add corpus data (general knowledge)
        for text in corpus_data[:5000]:  # Limit for memory efficiency
            texts.append(text)
            labels.append('general_knowledge')
        
        # Add emotion data
        for item in emotion_data:
            if item['query'] and item['emotion_response']:
                texts.append(f"{item['query']} {item['emotion_response']}")
                labels.append(f"emotion_{item['emotion']}")
        
        # Add domain-specific data
        domain_samples = {}
        for item in json_data:
            domain = item['domain']
            if domain not in domain_samples:
                domain_samples[domain] = []
            
            if len(domain_samples[domain]) < 200:  # Limit per domain
                domain_samples[domain].append(item['text_content'])
        
        for domain, domain_texts in domain_samples.items():
            texts.extend(domain_texts)
            labels.extend([f"domain_{domain}"] * len(domain_texts))
        
        print(f"📊 Dataset created: {len(texts):,} samples, {len(set(labels))} classes")
        
        # Vectorize text
        print("🔤 Vectorizing text...")
        X = self.vectorizer.fit_transform(texts).toarray().astype(np.float32)
        
        # Encode labels
        y = self.label_encoder.fit_transform(labels)
        
        class_names = self.label_encoder.classes_
        
        print(f"✅ Features: {X.shape[1]}, Classes: {len(class_names)}")
        print(f"Sample classes: {list(class_names[:10])}")
        
        return X, y, class_names
    
    def create_regression_dataset(self) -> Tuple[np.ndarray, np.ndarray]:
        """Create a regression dataset for confidence/importance prediction."""
        print("\n📈 Creating regression dataset...")
        
        json_data = self.load_raw_json_data()
        
        texts = []
        scores = []
        
        for item in json_data:
            if 'metadata' in item and 'score' in item['metadata']:
                texts.append(item['text_content'])
                scores.append(float(item['metadata']['score']))
        
        if len(texts) == 0:
            print("⚠️  No regression data found")
            return np.array([]), np.array([])
        
        print(f"📊 Regression dataset: {len(texts):,} samples")
        
        # Vectorize
        X = self.vectorizer.fit_transform(texts).toarray().astype(np.float32)
        y = np.array(scores, dtype=np.float32)
        
        print(f"✅ Features: {X.shape[1]}, Score range: {y.min():.3f} - {y.max():.3f}")
        
        return X, y
    
    def save_processed_data(self, X: np.ndarray, y: np.ndarray, 
                          filename: str = "arkona_processed_data.npz"):
        """Save processed data for later use."""
        save_path = os.path.join(self.base_path, filename)
        
        np.savez_compressed(save_path, X=X, y=y)
        print(f"💾 Saved processed data to: {save_path}")
        
        return save_path


def main():
    """Main processing function."""
    print("🚀 ARKONA DATA PROCESSING FOR LAYERMATRIX")
    print("=" * 60)
    
    processor = ArkonaDataProcessor()
    
    # Create classification dataset
    X_class, y_class, class_names = processor.create_classification_dataset()
    
    if len(X_class) > 0:
        # Save classification data
        processor.save_processed_data(X_class, y_class, "arkona_classification_data.npz")
        
        print(f"\n📊 CLASSIFICATION DATASET READY:")
        print(f"   Samples: {X_class.shape[0]:,}")
        print(f"   Features: {X_class.shape[1]:,}")
        print(f"   Classes: {len(class_names)}")
        print(f"   Class distribution: {np.bincount(y_class)[:10]}")
    
    # Create regression dataset
    X_reg, y_reg = processor.create_regression_dataset()
    
    if len(X_reg) > 0:
        processor.save_processed_data(X_reg, y_reg, "arkona_regression_data.npz")
        
        print(f"\n📈 REGRESSION DATASET READY:")
        print(f"   Samples: {X_reg.shape[0]:,}")
        print(f"   Features: {X_reg.shape[1]:,}")
        print(f"   Score stats: μ={y_reg.mean():.3f}, σ={y_reg.std():.3f}")
    
    print(f"\n✅ Data processing complete! Ready for LayerMatrix training.")


if __name__ == "__main__":
    main()
