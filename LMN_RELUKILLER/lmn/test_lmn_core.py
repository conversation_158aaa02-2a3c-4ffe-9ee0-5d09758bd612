#!/usr/bin/env python3
"""
Core tests for LMN module - focused on testing the main functionality
without package import dependencies.
"""

import unittest
import numpy as np
import pandas as pd
import json
import tempfile
import os
from unittest.mock import patch, mock_open, MagicMock
import sys
import warnings
warnings.filterwarnings('ignore')

# Direct import from LMN.py file
import importlib.util
lmn_path = os.path.join(os.path.dirname(__file__), 'lmn', 'LMN.py')
spec = importlib.util.spec_from_file_location("LMN", lmn_path)
if spec and spec.loader:
    LMN_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(LMN_module)
    
    LayerMatrix = LMN_module.LayerMatrix
    process_emotion_data = LMN_module.process_emotion_data
    process_training_data = LMN_module.process_training_data
    inspect_full_dataset = LMN_module.inspect_full_dataset
    create_training_prompts = LMN_module.create_training_prompts
    create_knowledge_corpus = LMN_module.create_knowledge_corpus
    discover_data_format = LMN_module.discover_data_format
else:
    raise ImportError("Could not import LMN module")


class TestLayerMatrixCore(unittest.TestCase):
    """Core tests for the LayerMatrix neural network class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        with patch('builtins.print'):  # Suppress initialization prints
            self.model = LayerMatrix(
                hidden_layers=[32, 16], 
                accumulation_ratio=0.3,
                learning_rate=0.01,
                l2_reg=0.001,
                batch_size=16
            )
        
        # Create sample data for testing
        np.random.seed(42)
        self.X_train = np.random.randn(50, 8)
        self.y_train = np.random.randint(0, 2, 50)
        self.X_test = np.random.randn(10, 8)
        self.y_test = np.random.randint(0, 2, 10)
    
    def test_initialization(self):
        """Test LayerMatrix initialization."""
        self.assertEqual(self.model.hidden_layers, [32, 16])
        self.assertEqual(self.model.accumulation_ratio, 0.3)
        self.assertEqual(self.model.learning_rate, 0.01)
        self.assertEqual(self.model.l2_reg, 0.001)
        self.assertEqual(self.model.batch_size, 16)
        self.assertEqual(self.model.layers, [])
        self.assertIn('loss', self.model.training_history)
        self.assertIn('accuracy', self.model.training_history)
    
    def test_layer_initialization(self):
        """Test layer initialization."""
        input_size, output_size = 8, 1
        self.model._initialize_layers(input_size, output_size)
        
        # Check that layers are created
        expected_layer_count = len(self.model.hidden_layers) + 1  # hidden + output
        self.assertEqual(len(self.model.layers), expected_layer_count)
        
        # Check layer structure
        for layer in self.model.layers:
            self.assertIn('weights', layer)
            self.assertIn('bias', layer)
            self.assertIn('velocity_w', layer)
            self.assertIn('velocity_b', layer)
        
        # Check dimensions
        self.assertEqual(self.model.layers[0]['weights'].shape, (input_size, 32))
        self.assertEqual(self.model.layers[1]['weights'].shape, (32, 16))
        self.assertEqual(self.model.layers[2]['weights'].shape, (16, output_size))
    
    def test_forward_pass(self):
        """Test forward pass through the network."""
        self.model._initialize_layers(8, 1)
        output, layer_outputs = self.model._layermatrix_forward(self.X_test)
        
        # Check output shape
        self.assertEqual(output.shape, (10, 1))
        
        # Check that output is between 0 and 1 (sigmoid activation)
        self.assertTrue(np.all(output >= 0))
        self.assertTrue(np.all(output <= 1))
        
        # Check layer outputs
        self.assertEqual(len(layer_outputs), len(self.model.layers) + 1)
    
    def test_training_and_prediction(self):
        """Test training and prediction pipeline."""
        # Suppress print statements during testing
        with patch('builtins.print'):
            model = self.model.fit(self.X_train, self.y_train, epochs=3, verbose=False)
        
        # Check that model is returned
        self.assertIs(model, self.model)
        
        # Check that layers are initialized
        self.assertGreater(len(self.model.layers), 0)
        
        # Check that training history is populated
        self.assertGreater(len(self.model.training_history['loss']), 0)
        self.assertGreater(len(self.model.training_history['accuracy']), 0)
        
        # Test prediction
        predictions = self.model.predict(self.X_test)
        self.assertEqual(predictions.shape, (10,))
        self.assertTrue(np.all(np.isin(predictions, [0, 1])))
        
        # Test scoring
        score = self.model.score(self.X_test, self.y_test)
        self.assertGreaterEqual(score, 0)
        self.assertLessEqual(score, 1)


class TestDataProcessingCore(unittest.TestCase):
    """Core tests for data processing utility functions."""
    
    def setUp(self):
        """Set up test data."""
        self.sample_emotion_data = {
            "examples": {
                "emotion_context": [
                    {
                        "emotional_state": {"label": "happy"},
                        "standard_response": {"content": "Standard response"},
                        "emotion_adapted_response": {"content": "Happy response"}
                    },
                    {
                        "emotional_state": {"label": "sad"},
                        "standard_response": {"content": "Another standard"},
                        "emotion_adapted_response": {"content": "Sad response"}
                    }
                ]
            }
        }
        
        self.sample_training_data = {
            "examples": {
                "internal_training": [
                    {
                        "confidence_assessment": {
                            "current_confidence": 0.7,
                            "target_confidence": 0.9
                        },
                        "training_configuration": {
                            "automation_cost_monthly": "$1,500-2,000",
                            "epochs": 100,
                            "learning_rate": 0.01
                        }
                    }
                ]
            }
        }
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('json.load')
    def test_process_emotion_data(self, mock_json_load, mock_file):
        """Test emotion data processing."""
        mock_json_load.return_value = self.sample_emotion_data
        
        with patch('builtins.print'):
            result = process_emotion_data('test_file.json')
        
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 2)
        self.assertIn('emotion', result.columns)
        self.assertIn('standard_text', result.columns)
        self.assertIn('adapted_text', result.columns)
        
        # Check data content
        self.assertEqual(result.iloc[0]['emotion'], 'happy')
        self.assertEqual(result.iloc[1]['emotion'], 'sad')
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('json.load')
    def test_process_training_data(self, mock_json_load, mock_file):
        """Test training data processing."""
        mock_json_load.return_value = self.sample_training_data
        
        with patch('builtins.print'):
            result = process_training_data('test_file.json')
        
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(len(result), 1)
        
        # Check required columns
        expected_columns = [
            'confidence_current', 'confidence_target', 'automation_cost_monthly',
            'training_epochs', 'learning_rate', 'domain_complexity', 'success_probability'
        ]
        for col in expected_columns:
            self.assertIn(col, result.columns)
    
    def test_create_training_prompts(self):
        """Test creation of training prompts from emotion data."""
        # Create sample DataFrame
        df = pd.DataFrame({
            'emotion': ['happy', 'sad'],
            'standard_text': ['Standard response 1', 'Standard response 2'],
            'adapted_text': ['Happy response', 'Sad response']
        })
        
        with patch('builtins.print'):
            result = create_training_prompts(df)
        
        self.assertIn('training_prompt', result.columns)
        self.assertEqual(len(result), 2)
        
        # Check prompt format
        prompt1 = result.iloc[0]['training_prompt']
        self.assertIn("Adapt the following text to sound more 'happy'", prompt1)
        self.assertIn('Standard response 1', prompt1)
        self.assertIn('### Adapted Text:', prompt1)


class TestUtilityFunctionsCore(unittest.TestCase):
    """Core tests for utility functions."""
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('json.load')
    def test_discover_data_format(self, mock_json_load, mock_file):
        """Test data format discovery."""
        test_data = {"key1": "value1", "key2": [1, 2, 3]}
        mock_json_load.return_value = test_data
        
        with patch('builtins.print'):
            result = discover_data_format('test_file.json')
        
        self.assertEqual(result, test_data)
    
    @patch('builtins.open', side_effect=FileNotFoundError)
    def test_discover_data_format_file_not_found(self, mock_file):
        """Test data format discovery with missing file."""
        with patch('builtins.print'):
            result = discover_data_format('nonexistent.json')
        
        self.assertIsNone(result)


if __name__ == '__main__':
    print("Running LMN Core Tests...")
    print("=" * 50)
    unittest.main(verbosity=2)
