from setuptools import setup, find_packages

setup(
    name='lmn-ai',
    version='0.1.0',
    description='LayerMatrix AI Network - Advanced neural network for AI specialist training and text processing',
    author='<PERSON>',
    author_email='<EMAIL>',
    packages=find_packages(),
    install_requires=[
        'numpy',
        'pandas',
        'scikit-learn',
        'matplotlib',
        'seaborn'
    ],
    python_requires='>=3.7',
    classifiers=[
        'Programming Language :: Python :: 3',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
    ],
)
