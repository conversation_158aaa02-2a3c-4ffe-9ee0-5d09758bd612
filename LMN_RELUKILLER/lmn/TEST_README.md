# LMN Module Test Suite

This directory contains comprehensive tests for the LMN (LayerMatrix Network) module. The tests are designed to validate the functionality of the neural network implementation and data processing utilities.

## Test Files

### 1. `test_lmn.py` - Complete Test Suite
The main test file containing comprehensive tests for all LMN functionality:

- **TestLayerMatrix**: Tests for the LayerMatrix neural network class
- **TestDataProcessingFunctions**: Tests for data processing utilities
- **TestUtilityFunctions**: Tests for utility functions
- **TestIntegrationFunctions**: Integration tests for complex workflows

### 2. `test_lmn_core.py` - Core Functionality Tests
A focused test suite that avoids package import dependencies:

- **TestLayerMatrixCore**: Core neural network functionality
- **TestDataProcessingCore**: Essential data processing functions
- **TestUtilityFunctionsCore**: Core utility functions

### 3. `run_tests.py` - Test Runner
A test runner script that discovers and executes all tests with detailed reporting.

## Running the Tests

### Option 1: Run All Tests
```bash
python3 run_tests.py
```

### Option 2: Run Core Tests Only
```bash
python3 test_lmn_core.py
```

### Option 3: Run Specific Test Classes
```bash
python3 -m unittest test_lmn.TestLayerMatrix
python3 -m unittest test_lmn.TestDataProcessingFunctions
```

## Test Coverage

### LayerMatrix Neural Network Tests

#### Initialization Tests
- ✅ Parameter validation (hidden_layers, learning_rate, etc.)
- ✅ Training history initialization
- ✅ Layer structure setup

#### Architecture Tests
- ✅ Layer initialization with correct dimensions
- ✅ Weight and bias initialization
- ✅ Velocity vectors for momentum

#### Forward Pass Tests
- ✅ Input propagation through layers
- ✅ Activation functions (tanh, sigmoid)
- ✅ Output shape validation
- ✅ Output range validation (0-1 for sigmoid)

#### Training Tests
- ✅ Fit method functionality
- ✅ Training history tracking
- ✅ Batch processing
- ✅ Validation split handling

#### Prediction Tests
- ✅ Predict method output format
- ✅ Binary classification output (0/1)
- ✅ Score method accuracy calculation

#### Visualization Tests
- ✅ Training history plotting
- ✅ Handling empty training history

### Data Processing Tests

#### Emotion Data Processing
- ✅ JSON file loading and parsing
- ✅ DataFrame creation with correct columns
- ✅ Data extraction from nested JSON structure
- ✅ Error handling for missing files
- ✅ Error handling for malformed data

#### Training Data Processing
- ✅ Internal training data extraction
- ✅ Cost parsing from range strings
- ✅ Domain complexity calculation
- ✅ Success probability computation
- ✅ DataFrame structure validation

#### Training Prompt Generation
- ✅ Prompt formatting for fine-tuning
- ✅ Template structure validation
- ✅ Emotion-specific adaptation

### Utility Function Tests

#### Dataset Inspection
- ✅ Full dataset structure analysis
- ✅ Category counting and reporting
- ✅ Sample record display

#### Knowledge Corpus Creation
- ✅ Text extraction from multiple categories
- ✅ File writing functionality
- ✅ Text filtering and cleaning

#### Data Format Discovery
- ✅ JSON structure analysis
- ✅ Recursive data exploration
- ✅ Type identification and reporting

## Test Results Summary

### Latest Test Run Results
- **Total Tests**: 23
- **Passed**: 22 (95.7% success rate)
- **Failed**: 0
- **Errors**: 1 (import dependency issue)

### Core Tests Results
- **Total Tests**: 9
- **Passed**: 9 (100% success rate)
- **Failed**: 0
- **Errors**: 0

## Known Issues

1. **Import Dependency**: Some tests may fail due to missing `datasets` package dependency in the `load_dataset.py` module. This doesn't affect core functionality.

2. **Mock Warnings**: Some IDE warnings about unused mock parameters - these are intentional for proper test isolation.

## Test Data

The tests use mock data structures that mirror the expected format of:
- Emotion context data with emotional states and responses
- Internal training data with confidence assessments
- Various JSON structures for utility function testing

## Adding New Tests

When adding new tests:

1. Follow the existing naming convention: `test_<function_name>`
2. Use appropriate mocking for external dependencies
3. Include both success and error cases
4. Add docstrings explaining the test purpose
5. Update this README with new test coverage

## Dependencies

The tests require:
- `unittest` (Python standard library)
- `numpy`
- `pandas`
- `json` (Python standard library)
- `unittest.mock` (Python standard library)
- `sklearn` (for some integration tests)

## Continuous Integration

These tests are designed to be run in CI/CD pipelines and provide comprehensive validation of the LMN module functionality.
