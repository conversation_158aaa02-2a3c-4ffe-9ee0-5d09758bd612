#!/usr/bin/env python3
"""
LayerMatrix Data Augmentation with CLI Generator

Integrates the CLI generator to create fresh training data for LayerMatrix
while training is in progress. Supports:
- Real-time data generation
- Domain-specific augmentation
- Adaptive sample generation based on training performance
- Seamless integration with existing training pipeline
"""

import os
import json
import subprocess
import numpy as np
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
import threading
from queue import Queue

class LayerMatrixDataAugmenter:
    """Augment LayerMatrix training with fresh generated data."""
    
    def __init__(self, 
                 generator_path: str = "/Users/<USER>/Desktop/arkona_train/generator/generate",
                 arkona_sources_path: str = "/Users/<USER>/Desktop/arkona_train/LMN_RELUKILLER/arkona_model/training_data/sources"):
        
        self.generator_path = Path(generator_path)
        self.arkona_sources_path = Path(arkona_sources_path)
        self.cli_generator = self.generator_path / "cli-generator.js"
        
        # Output directories
        self.output_dir = Path("generated_training_data")
        self.output_dir.mkdir(exist_ok=True)
        
        # Generation queue for background processing
        self.generation_queue = Queue()
        self.generated_data = []
        
        print(f"🚀 LayerMatrix Data Augmenter initialized")
        print(f"   Generator: {self.cli_generator}")
        print(f"   Sources: {self.arkona_sources_path}")
        print(f"   Output: {self.output_dir}")
    
    def check_generator_available(self) -> bool:
        """Check if the CLI generator is available."""
        if not self.cli_generator.exists():
            print(f"❌ CLI generator not found at: {self.cli_generator}")
            return False
        
        # Check if Node.js is available
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ Node.js available: {result.stdout.strip()}")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Node.js not found. Please install Node.js to use the generator.")
            return False
        
        return False
    
    def find_source_files(self, domain: Optional[str] = None) -> List[Path]:
        """Find source files for data generation."""
        source_files = []
        
        # Look in raw_json directory
        raw_json_dir = self.arkona_sources_path / "raw_json"
        if raw_json_dir.exists():
            if domain:
                # Look for domain-specific files
                domain_dir = raw_json_dir / domain
                if domain_dir.exists():
                    source_files.extend(domain_dir.glob("*.json"))
            else:
                # Get all JSON files
                for subdir in raw_json_dir.iterdir():
                    if subdir.is_dir():
                        source_files.extend(subdir.glob("*.json"))
        
        # Look for other source files
        for pattern in ["*.txt", "*.md", "*.json"]:
            source_files.extend(self.arkona_sources_path.glob(f"**/{pattern}"))
        
        return source_files[:10]  # Limit to prevent overwhelming
    
    def generate_qa_pairs(self, source_file: Path, count: int = 1000, 
                         domain: Optional[str] = None) -> Optional[Path]:
        """Generate Q&A pairs from a source file using the CLI generator."""
        
        if not self.check_generator_available():
            return None
        
        # Create output filename
        timestamp = int(time.time())
        domain_suffix = f"_{domain}" if domain else ""
        output_file = self.output_dir / f"qa_pairs_{source_file.stem}{domain_suffix}_{timestamp}.json"
        
        print(f"🔄 Generating {count} Q&A pairs from: {source_file.name}")
        
        try:
            # Run the CLI generator
            cmd = [
                'node', str(self.cli_generator),
                str(source_file),
                str(output_file),
                '--layermatrix',
                '--count', str(count)
            ]
            
            result = subprocess.run(cmd, 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=300,  # 5 minute timeout
                                  cwd=self.generator_path)
            
            if result.returncode == 0:
                print(f"✅ Generated Q&A pairs: {output_file}")
                return output_file
            else:
                print(f"❌ Generator failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Generator timed out for: {source_file.name}")
            return None
        except Exception as e:
            print(f"❌ Error running generator: {e}")
            return None
    
    def load_generated_data(self, qa_file: Path) -> Optional[Dict]:
        """Load generated Q&A data and convert to training format."""
        try:
            with open(qa_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'training_data' in data:
                return data
            else:
                print(f"⚠️  Unexpected format in: {qa_file}")
                return None
                
        except Exception as e:
            print(f"❌ Error loading generated data: {e}")
            return None
    
    def convert_to_layermatrix_format(self, qa_data: Dict) -> tuple:
        """Convert Q&A data to LayerMatrix training format (X, y)."""
        training_samples = qa_data.get('training_data', [])
        
        if not training_samples:
            return np.array([]), np.array([])
        
        # For now, create a simple text classification task
        # In practice, you'd use proper text vectorization
        texts = []
        labels = []
        
        for sample in training_samples:
            # Combine question and answer as input text
            combined_text = f"{sample['input_text']} {sample['target_text']}"
            texts.append(combined_text)
            
            # Use domain as label (you could also use other classification schemes)
            domain = sample.get('domain', 'general')
            labels.append(domain)
        
        return texts, labels
    
    def augment_training_data(self, domains: List[str] = None, 
                            samples_per_domain: int = 500) -> Dict[str, Any]:
        """Generate augmented training data for specified domains."""
        
        if not domains:
            domains = ['psychology', 'finance', 'ai_optimization', 'philosophy', 'medical']
        
        print(f"🎯 Augmenting training data for domains: {domains}")
        
        augmented_data = {
            'metadata': {
                'generated_at': time.time(),
                'domains': domains,
                'samples_per_domain': samples_per_domain,
                'total_files': 0
            },
            'domain_data': {}
        }
        
        for domain in domains:
            print(f"\n📊 Processing domain: {domain}")
            
            # Find source files for this domain
            source_files = self.find_source_files(domain)
            
            if not source_files:
                print(f"⚠️  No source files found for domain: {domain}")
                continue
            
            domain_qa_files = []
            
            # Generate Q&A pairs from each source file
            for source_file in source_files[:3]:  # Limit to 3 files per domain
                qa_file = self.generate_qa_pairs(
                    source_file, 
                    count=samples_per_domain // len(source_files[:3]),
                    domain=domain
                )
                
                if qa_file:
                    domain_qa_files.append(qa_file)
            
            # Load and combine domain data
            domain_texts = []
            domain_labels = []
            
            for qa_file in domain_qa_files:
                qa_data = self.load_generated_data(qa_file)
                if qa_data:
                    texts, labels = self.convert_to_layermatrix_format(qa_data)
                    domain_texts.extend(texts)
                    domain_labels.extend(labels)
            
            augmented_data['domain_data'][domain] = {
                'texts': domain_texts,
                'labels': domain_labels,
                'sample_count': len(domain_texts),
                'source_files': [str(f) for f in domain_qa_files]
            }
            
            print(f"   ✅ Generated {len(domain_texts)} samples for {domain}")
        
        # Save combined augmented data
        output_file = self.output_dir / f"augmented_training_data_{int(time.time())}.json"
        
        # Convert numpy arrays to lists for JSON serialization
        serializable_data = augmented_data.copy()
        for domain, data in serializable_data['domain_data'].items():
            data['texts'] = list(data['texts'])
            data['labels'] = list(data['labels'])
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Saved augmented data: {output_file}")
        
        return augmented_data
    
    def background_generation(self, domains: List[str], samples_per_domain: int = 200):
        """Generate training data in the background while LayerMatrix trains."""
        
        def generation_worker():
            print("🔄 Starting background data generation...")
            
            while True:
                try:
                    # Check if there are generation requests
                    if not self.generation_queue.empty():
                        request = self.generation_queue.get()
                        domain = request.get('domain')
                        count = request.get('count', samples_per_domain)
                        
                        print(f"🎯 Background generating for domain: {domain}")
                        
                        # Find and process source files
                        source_files = self.find_source_files(domain)
                        for source_file in source_files[:2]:  # Limit for background processing
                            qa_file = self.generate_qa_pairs(source_file, count=count//2, domain=domain)
                            if qa_file:
                                qa_data = self.load_generated_data(qa_file)
                                if qa_data:
                                    self.generated_data.append(qa_data)
                        
                        self.generation_queue.task_done()
                    
                    time.sleep(10)  # Check every 10 seconds
                    
                except Exception as e:
                    print(f"❌ Background generation error: {e}")
                    time.sleep(30)
        
        # Start background thread
        thread = threading.Thread(target=generation_worker, daemon=True)
        thread.start()
        
        # Queue initial generation requests
        for domain in domains:
            self.generation_queue.put({'domain': domain, 'count': samples_per_domain})
        
        print(f"🚀 Background generation started for {len(domains)} domains")
    
    def get_generated_samples(self) -> List[Dict]:
        """Get all generated samples from background processing."""
        return self.generated_data.copy()


def main():
    """Test the data augmentation system."""
    print("🧠 LAYERMATRIX DATA AUGMENTATION TEST")
    print("=" * 60)
    
    augmenter = LayerMatrixDataAugmenter()
    
    if not augmenter.check_generator_available():
        print("❌ Generator not available. Please check setup.")
        return
    
    # Test with a few domains
    test_domains = ['psychology', 'ai_optimization']
    
    print(f"\n🎯 Testing augmentation with domains: {test_domains}")
    
    # Generate augmented data
    augmented_data = augmenter.augment_training_data(
        domains=test_domains,
        samples_per_domain=100  # Small test
    )
    
    # Print summary
    total_samples = sum(
        data['sample_count'] 
        for data in augmented_data['domain_data'].values()
    )
    
    print(f"\n📊 AUGMENTATION COMPLETE:")
    print(f"   Total samples generated: {total_samples}")
    print(f"   Domains processed: {len(augmented_data['domain_data'])}")
    print(f"   Ready for LayerMatrix training!")


if __name__ == "__main__":
    main()
