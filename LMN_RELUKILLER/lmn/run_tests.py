#!/usr/bin/env python3
"""
Comprehensive test runner for LMN module tests.
This script runs all tests including comparison benchmarks and provides detailed results.
"""

import sys
import os
import unittest
import argparse
from io import StringIO

# Add the current directory to the path so we can import the test module
sys.path.insert(0, os.path.dirname(__file__))

def run_core_tests():
    """Run core functionality tests only."""
    print("🧠 Running LMN Core Tests...")
    print("=" * 50)

    loader = unittest.TestLoader()
    suite = loader.loadTestsFromName('test_lmn_core')

    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    return result.wasSuccessful()

def run_comparison_tests():
    """Run comparison benchmark tests."""
    print("\n🥊 Running LMN Comparison Benchmarks...")
    print("=" * 50)

    try:
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName('test_lmn.TestLayerMatrixComparison')

        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)

        return result.wasSuccessful()
    except ImportError as e:
        print(f"⚠️  Skipping comparison tests: {e}")
        print("   Install sklearn to run comparison benchmarks")
        return True

def run_all_tests():
    """Run all tests and return results."""
    print("🚀 Running Complete LMN Test Suite...")
    print("=" * 70)

    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    suite = loader.discover(start_dir, pattern='test_*.py')

    # Capture test output for summary
    stream = StringIO()
    runner = unittest.TextTestRunner(stream=stream, verbosity=1)
    result = runner.run(suite)

    # Print detailed results
    print("\n" + "=" * 70)
    print("LMN MODULE TEST RESULTS SUMMARY")
    print("=" * 70)

    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")

    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}")
            print(f"  {traceback.split('AssertionError:')[-1].strip()}")

    if result.errors:
        print("\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}")
            # Extract the main error message
            error_lines = traceback.split('\n')
            for line in error_lines:
                if 'Error:' in line or 'Exception:' in line:
                    print(f"  {line.strip()}")
                    break

    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0
    print(f"\nSuccess Rate: {success_rate:.1f}%")

    if result.wasSuccessful():
        print("🎉 ALL TESTS PASSED!")
        print("\n📊 For detailed benchmark results, see BENCHMARK_RESULTS.md")
        return True
    else:
        print("❌ Some tests failed. See details above.")
        return False

def main():
    """Main test runner with command line options."""
    parser = argparse.ArgumentParser(description='LMN Test Runner')
    parser.add_argument('--core', action='store_true',
                       help='Run only core functionality tests')
    parser.add_argument('--benchmark', action='store_true',
                       help='Run only comparison benchmark tests')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')

    args = parser.parse_args()

    if args.core:
        success = run_core_tests()
    elif args.benchmark:
        success = run_comparison_tests()
    else:
        success = run_all_tests()

    if success:
        print("\n✅ Test run completed successfully!")
        if not args.core and not args.benchmark:
            print("📈 Check BENCHMARK_RESULTS.md for performance analysis")
    else:
        print("\n❌ Test run completed with failures")

    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
