#!/usr/bin/env python3
"""
LayerMatrix Training Logger

Comprehensive logging system for LayerMatrix training to track:
- Training progress and metrics
- GPU utilization and memory usage
- Accuracy improvements over time
- LayerMatrix-specific metrics (accumulation effectiveness)
- Real-world performance on Arkona data
"""

import logging
import time
import json
import os
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import threading

class LayerMatrixLogger:
    """Comprehensive logger for LayerMatrix training and evaluation."""
    
    def __init__(self, log_dir: str = "logs", experiment_name: str = None):
        self.log_dir = log_dir
        self.experiment_name = experiment_name or f"layermatrix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create log directory
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up file paths
        self.log_file = os.path.join(log_dir, f"{self.experiment_name}.log")
        self.metrics_file = os.path.join(log_dir, f"{self.experiment_name}_metrics.json")
        self.progress_file = os.path.join(log_dir, f"{self.experiment_name}_progress.json")
        
        # Initialize logging
        self._setup_logging()
        
        # Training metrics storage
        self.metrics = {
            'training_history': [],
            'gpu_utilization': [],
            'memory_usage': [],
            'accumulation_effectiveness': [],
            'layer_statistics': [],
            'accuracy_progression': [],
            'timing_data': {}
        }
        
        # Training state
        self.start_time = None
        self.current_epoch = 0
        self.current_batch = 0
        self.best_accuracy = 0.0
        
        self.logger.info(f"🚀 LayerMatrix Logger initialized: {self.experiment_name}")
        self.logger.info(f"Log files: {self.log_file}, {self.metrics_file}")
    
    def _setup_logging(self):
        """Set up logging configuration."""
        # Create logger
        self.logger = logging.getLogger(f"LayerMatrix_{self.experiment_name}")
        self.logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # File handler
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def log_training_start(self, model_config: Dict, dataset_info: Dict):
        """Log the start of training with model and dataset information."""
        self.start_time = time.time()
        
        self.logger.info("=" * 80)
        self.logger.info("🧠 LAYERMATRIX TRAINING STARTED")
        self.logger.info("=" * 80)
        
        # Log model configuration
        self.logger.info("🏗️  Model Configuration:")
        for key, value in model_config.items():
            self.logger.info(f"   {key}: {value}")
        
        # Log dataset information
        self.logger.info("📊 Dataset Information:")
        for key, value in dataset_info.items():
            self.logger.info(f"   {key}: {value}")
        
        # Store in metrics
        self.metrics['model_config'] = model_config
        self.metrics['dataset_info'] = dataset_info
        self.metrics['training_start_time'] = datetime.now().isoformat()
    
    def log_epoch_start(self, epoch: int, total_epochs: int):
        """Log the start of an epoch."""
        self.current_epoch = epoch
        self.logger.info(f"📈 Epoch {epoch + 1}/{total_epochs} started")
    
    def log_batch_progress(self, batch: int, total_batches: int, loss: float, 
                          accuracy: Optional[float] = None, gpu_memory: Optional[float] = None):
        """Log batch-level progress."""
        self.current_batch = batch
        
        # Calculate progress
        progress = (batch + 1) / total_batches * 100
        
        # Log message
        msg = f"   Batch {batch + 1:4d}/{total_batches} ({progress:5.1f}%) | Loss: {loss:.6f}"
        if accuracy is not None:
            msg += f" | Acc: {accuracy:.3f}"
        if gpu_memory is not None:
            msg += f" | GPU: {gpu_memory:.1f}GB"
        
        self.logger.debug(msg)
        
        # Store metrics
        batch_metrics = {
            'epoch': self.current_epoch,
            'batch': batch,
            'loss': loss,
            'accuracy': accuracy,
            'gpu_memory': gpu_memory,
            'timestamp': time.time() - self.start_time if self.start_time else 0
        }
        self.metrics['training_history'].append(batch_metrics)
    
    def log_epoch_end(self, epoch: int, epoch_loss: float, epoch_accuracy: float, 
                     val_loss: Optional[float] = None, val_accuracy: Optional[float] = None,
                     learning_rate: Optional[float] = None):
        """Log the end of an epoch with summary statistics."""
        
        # Calculate epoch time
        epoch_time = time.time() - self.start_time if self.start_time else 0
        
        # Log summary
        msg = f"✅ Epoch {epoch + 1} complete | Loss: {epoch_loss:.6f} | Acc: {epoch_accuracy:.3f}"
        if val_loss is not None and val_accuracy is not None:
            msg += f" | Val Loss: {val_loss:.6f} | Val Acc: {val_accuracy:.3f}"
        if learning_rate is not None:
            msg += f" | LR: {learning_rate:.2e}"
        
        self.logger.info(msg)
        
        # Track best accuracy
        current_best = val_accuracy if val_accuracy is not None else epoch_accuracy
        if current_best > self.best_accuracy:
            self.best_accuracy = current_best
            self.logger.info(f"🏆 New best accuracy: {self.best_accuracy:.3f}")
        
        # Store epoch metrics
        epoch_metrics = {
            'epoch': epoch,
            'train_loss': epoch_loss,
            'train_accuracy': epoch_accuracy,
            'val_loss': val_loss,
            'val_accuracy': val_accuracy,
            'learning_rate': learning_rate,
            'epoch_time': epoch_time,
            'best_accuracy': self.best_accuracy
        }
        self.metrics['accuracy_progression'].append(epoch_metrics)
    
    def log_layermatrix_metrics(self, accumulation_effectiveness: float, 
                               layer_activations: List[Dict], global_context_stats: Dict):
        """Log LayerMatrix-specific metrics."""
        
        self.logger.debug(f"🔄 Accumulation effectiveness: {accumulation_effectiveness:.3f}")
        self.logger.debug(f"🌐 Global context stats: {global_context_stats}")
        
        # Store LayerMatrix metrics
        lm_metrics = {
            'epoch': self.current_epoch,
            'accumulation_effectiveness': accumulation_effectiveness,
            'layer_activations': layer_activations,
            'global_context_stats': global_context_stats,
            'timestamp': time.time() - self.start_time if self.start_time else 0
        }
        self.metrics['accumulation_effectiveness'].append(lm_metrics)
    
    def log_gpu_utilization(self, gpu_memory_used: float, gpu_memory_total: float,
                           compute_utilization: Optional[float] = None):
        """Log GPU utilization metrics."""
        
        memory_percent = (gpu_memory_used / gpu_memory_total) * 100
        
        msg = f"🖥️  GPU Memory: {gpu_memory_used:.1f}/{gpu_memory_total:.1f}GB ({memory_percent:.1f}%)"
        if compute_utilization is not None:
            msg += f" | Compute: {compute_utilization:.1f}%"
        
        self.logger.debug(msg)
        
        # Store GPU metrics
        gpu_metrics = {
            'epoch': self.current_epoch,
            'memory_used': gpu_memory_used,
            'memory_total': gpu_memory_total,
            'memory_percent': memory_percent,
            'compute_utilization': compute_utilization,
            'timestamp': time.time() - self.start_time if self.start_time else 0
        }
        self.metrics['gpu_utilization'].append(gpu_metrics)
    
    def log_training_end(self, final_accuracy: float, total_time: float, 
                        final_metrics: Dict):
        """Log the end of training with final results."""
        
        self.logger.info("=" * 80)
        self.logger.info("🏁 LAYERMATRIX TRAINING COMPLETED")
        self.logger.info("=" * 80)
        
        self.logger.info(f"🎯 Final Accuracy: {final_accuracy:.3f}")
        self.logger.info(f"🏆 Best Accuracy: {self.best_accuracy:.3f}")
        self.logger.info(f"⏱️  Total Time: {total_time:.2f}s ({total_time/60:.1f}m)")
        
        # Log final metrics
        self.logger.info("📊 Final Metrics:")
        for key, value in final_metrics.items():
            self.logger.info(f"   {key}: {value}")
        
        # Store final results
        self.metrics['training_end_time'] = datetime.now().isoformat()
        self.metrics['total_training_time'] = total_time
        self.metrics['final_accuracy'] = final_accuracy
        self.metrics['best_accuracy'] = self.best_accuracy
        self.metrics['final_metrics'] = final_metrics
        
        # Save all metrics to file
        self.save_metrics()
    
    def log_comparison_results(self, layermatrix_results: Dict, baseline_results: Dict):
        """Log comparison results between LayerMatrix and baselines."""
        
        self.logger.info("⚔️  COMPARISON RESULTS")
        self.logger.info("-" * 40)
        
        self.logger.info("LayerMatrix Results:")
        for key, value in layermatrix_results.items():
            self.logger.info(f"   {key}: {value}")
        
        self.logger.info("Baseline Results:")
        for key, value in baseline_results.items():
            self.logger.info(f"   {key}: {value}")
        
        # Calculate improvements
        if 'accuracy' in layermatrix_results and 'accuracy' in baseline_results:
            improvement = layermatrix_results['accuracy'] - baseline_results['accuracy']
            self.logger.info(f"🚀 Accuracy Improvement: {improvement:+.3f}")
        
        # Store comparison
        self.metrics['comparison_results'] = {
            'layermatrix': layermatrix_results,
            'baseline': baseline_results
        }
    
    def save_metrics(self):
        """Save all collected metrics to JSON file."""
        try:
            with open(self.metrics_file, 'w') as f:
                json.dump(self.metrics, f, indent=2, default=str)
            
            self.logger.info(f"💾 Metrics saved to: {self.metrics_file}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to save metrics: {e}")
    
    def save_progress_checkpoint(self):
        """Save current progress as checkpoint."""
        try:
            progress_data = {
                'experiment_name': self.experiment_name,
                'current_epoch': self.current_epoch,
                'current_batch': self.current_batch,
                'best_accuracy': self.best_accuracy,
                'elapsed_time': time.time() - self.start_time if self.start_time else 0,
                'timestamp': datetime.now().isoformat()
            }
            
            with open(self.progress_file, 'w') as f:
                json.dump(progress_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"❌ Failed to save progress: {e}")
    
    def log_error(self, error_msg: str, exception: Optional[Exception] = None):
        """Log errors with full context."""
        self.logger.error(f"❌ {error_msg}")
        if exception:
            self.logger.error(f"   Exception: {str(exception)}")
            self.logger.debug(f"   Full traceback:", exc_info=True)
    
    def log_warning(self, warning_msg: str):
        """Log warnings."""
        self.logger.warning(f"⚠️  {warning_msg}")
    
    def log_info(self, info_msg: str):
        """Log general information."""
        self.logger.info(f"ℹ️  {info_msg}")
    
    def log_debug(self, debug_msg: str):
        """Log debug information."""
        self.logger.debug(f"🔍 {debug_msg}")


# Convenience function for quick logger setup
def setup_layermatrix_logger(experiment_name: str = None, log_dir: str = "logs") -> LayerMatrixLogger:
    """Set up a LayerMatrix logger with default configuration."""
    return LayerMatrixLogger(log_dir=log_dir, experiment_name=experiment_name)


if __name__ == "__main__":
    # Test the logger
    logger = setup_layermatrix_logger("test_experiment")
    
    # Simulate training logging
    model_config = {
        'hidden_layers': [1024, 512, 256],
        'accumulation_ratio': 0.15,
        'learning_rate': 1e-4,
        'batch_size': 16
    }
    
    dataset_info = {
        'samples': 9456,
        'features': 2048,
        'classes': 24,
        'source': 'Arkona real-world data'
    }
    
    logger.log_training_start(model_config, dataset_info)
    
    # Simulate some training
    for epoch in range(3):
        logger.log_epoch_start(epoch, 3)
        
        for batch in range(5):
            loss = 0.5 - (epoch * 0.1 + batch * 0.02)
            accuracy = 0.6 + (epoch * 0.1 + batch * 0.02)
            logger.log_batch_progress(batch, 5, loss, accuracy, 6.5)
        
        logger.log_epoch_end(epoch, loss, accuracy, loss * 0.9, accuracy * 1.05, 1e-4)
    
    logger.log_training_end(0.85, 120.5, {'parameters': 1968896, 'throughput': '16K samples/s'})
    
    print("✅ Logger test completed! Check the logs directory.")
