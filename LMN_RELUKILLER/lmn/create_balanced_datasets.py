#!/usr/bin/env python3
"""
Create Balanced Datasets with Generate v2

Uses your CLI generator v2 to create perfectly balanced datasets
with even labels for high-accuracy LayerMatrix training.

Strategy:
1. Define target categories (wars, science, politics, etc.)
2. Use generate_v2 to create equal samples per category
3. Train LayerMatrix on balanced data for 80%+ accuracy
"""

import subprocess
import json
import time
import os
from pathlib import Path
from typing import List, Dict

class BalancedDatasetCreator:
    """Create balanced datasets using generate_v2 CLI."""
    
    def __init__(self):
        self.generator_path = Path("/Users/<USER>/Desktop/arkona_train/generator/generate_v2")
        self.cli_generator = self.generator_path / "cli-generator-v2.js"
        self.output_dir = Path("balanced_datasets")
        self.output_dir.mkdir(exist_ok=True)
        
        print("🎯 Balanced Dataset Creator initialized!")
        print(f"   Generator: {self.cli_generator}")
        print(f"   Output: {self.output_dir}")
    
    def check_generator_available(self) -> bool:
        """Check if generate_v2 CLI is available."""
        if not self.cli_generator.exists():
            print(f"❌ CLI generator v2 not found at: {self.cli_generator}")
            return False
        
        try:
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ Node.js available: {result.stdout.strip()}")
                return True
        except:
            print("❌ Node.js not found")
            return False
        
        return False
    
    def create_category_content_files(self):
        """Create content files for each category to feed to the generator."""
        
        categories = {
            "ancient_wars": """
            Ancient warfare and military conflicts from antiquity including:
            - Greek and Persian Wars (499-449 BCE)
            - Punic Wars between Rome and Carthage (264-146 BCE)
            - Alexander the Great's conquests (336-323 BCE)
            - Roman civil wars and expansion
            - Ancient Chinese warfare and the Warring States period
            - Egyptian military campaigns and battles
            - Assyrian and Babylonian conquests
            """,
            
            "medieval_wars": """
            Medieval warfare and conflicts from 500-1500 CE including:
            - The Crusades (1095-1291)
            - Hundred Years' War between England and France (1337-1453)
            - Viking raids and conquests (793-1066)
            - Mongol invasions and empire building (1206-1368)
            - Byzantine-Ottoman conflicts
            - Medieval siege warfare and castle battles
            - Wars of the Roses in England (1455-1487)
            """,
            
            "modern_wars": """
            Modern warfare from 1500-1900 including:
            - Napoleonic Wars (1803-1815)
            - American Civil War (1861-1865)
            - Seven Years' War (1756-1763)
            - Wars of Spanish Succession
            - Colonial wars and expansion
            - Revolutionary wars in America and France
            - Crimean War (1853-1856)
            """,
            
            "world_wars": """
            20th century global conflicts including:
            - World War I (1914-1918) - trench warfare, new technologies
            - World War II (1939-1945) - global scale, Holocaust, atomic weapons
            - Russian Revolution and Civil War (1917-1921)
            - Spanish Civil War (1936-1939)
            - Korean War (1950-1953)
            - Cold War conflicts and proxy wars
            """,
            
            "scientific_discoveries": """
            Major scientific breakthroughs and discoveries including:
            - Newton's laws of physics and gravity
            - Darwin's theory of evolution
            - Einstein's theory of relativity
            - Discovery of DNA structure
            - Quantum mechanics development
            - Medical breakthroughs like antibiotics
            - Space exploration and moon landing
            """,
            
            "political_events": """
            Major political events and changes including:
            - Formation of democratic governments
            - Revolutions and independence movements
            - Rise and fall of empires
            - Constitutional conventions and legal reforms
            - Civil rights movements
            - Decolonization processes
            - Formation of international organizations
            """
        }
        
        content_files = {}
        
        for category, content in categories.items():
            content_file = self.output_dir / f"{category}_content.txt"
            
            with open(content_file, 'w', encoding='utf-8') as f:
                f.write(content.strip())
            
            content_files[category] = content_file
            print(f"📝 Created content file: {category}")
        
        return content_files
    
    def generate_balanced_samples(self, content_files: Dict[str, Path], 
                                samples_per_category: int = 200) -> Dict[str, Path]:
        """Generate balanced samples for each category using generate_v2."""
        
        if not self.check_generator_available():
            print("❌ Generator not available")
            return {}
        
        generated_files = {}
        
        print(f"\n🤖 Generating {samples_per_category} samples per category...")
        
        for category, content_file in content_files.items():
            print(f"\n⚡ Generating samples for: {category}")
            
            output_file = self.output_dir / f"{category}_{samples_per_category}_samples.json"
            
            # Customize generation settings per category
            if "war" in category:
                sample_type = "Technical"
                persona = "Expert"
                tone = "Formal"
                instructions = f"Focus on {category.replace('_', ' ')} with specific battles, dates, and military strategies"
            elif "scientific" in category:
                sample_type = "Educational"
                persona = "Academic"
                tone = "Technical"
                instructions = f"Focus on scientific discoveries with explanations of methods and impact"
            else:
                sample_type = "Fact-based Q&A"
                persona = "Professional"
                tone = "Formal"
                instructions = f"Focus on {category.replace('_', ' ')} with specific dates and key figures"
            
            # Build command
            cmd = [
                'node', str(self.cli_generator),
                str(content_file),
                str(output_file),
                '--layermatrix',
                '--count', str(samples_per_category),
                '--type', sample_type,
                '--persona', persona,
                '--tone', tone,
                '--instructions', instructions
            ]
            
            try:
                print(f"   🔄 Running generator...")
                print(f"      Type: {sample_type}, Persona: {persona}, Tone: {tone}")
                
                result = subprocess.run(cmd, 
                                      capture_output=True, 
                                      text=True, 
                                      timeout=300,  # 5 minute timeout
                                      cwd=self.generator_path)
                
                if result.returncode == 0:
                    print(f"   ✅ Generated: {output_file}")
                    generated_files[category] = output_file
                else:
                    print(f"   ❌ Generation failed: {result.stderr}")
                
            except subprocess.TimeoutExpired:
                print(f"   ⏰ Generation timed out for: {category}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return generated_files
    
    def combine_balanced_dataset(self, generated_files: Dict[str, Path]) -> Path:
        """Combine all generated files into one balanced dataset."""
        
        print(f"\n🔄 Combining into balanced dataset...")
        
        combined_data = {
            "metadata": {
                "dataset_name": "balanced_layermatrix_training",
                "created_at": time.time(),
                "categories": list(generated_files.keys()),
                "samples_per_category": {},
                "total_samples": 0,
                "format": "layermatrix_balanced_training"
            },
            "training_data": []
        }
        
        total_samples = 0
        
        for category, file_path in generated_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                training_data = data.get('training_data', [])
                category_count = len(training_data)
                
                # Add category label to each sample
                for sample in training_data:
                    sample['category'] = category
                    sample['balanced_label'] = category  # Clear label for classification
                    combined_data['training_data'].append(sample)
                
                combined_data['metadata']['samples_per_category'][category] = category_count
                total_samples += category_count
                
                print(f"   📊 {category}: {category_count} samples")
                
            except Exception as e:
                print(f"   ⚠️  Error loading {file_path}: {e}")
        
        combined_data['metadata']['total_samples'] = total_samples
        
        # Save combined dataset
        combined_file = self.output_dir / f"balanced_dataset_{total_samples}_samples.json"
        
        with open(combined_file, 'w', encoding='utf-8') as f:
            json.dump(combined_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Balanced dataset created:")
        print(f"   File: {combined_file}")
        print(f"   Total samples: {total_samples:,}")
        print(f"   Categories: {len(generated_files)}")
        print(f"   Balance: {total_samples // len(generated_files)} samples per category")
        
        return combined_file
    
    def create_complete_balanced_dataset(self, samples_per_category: int = 150):
        """Create a complete balanced dataset ready for LayerMatrix training."""
        
        print("🎯 CREATING BALANCED DATASET WITH GENERATE V2")
        print("=" * 70)
        print(f"Target: {samples_per_category} samples per category")
        print("Strategy: Even labels for high-accuracy classification")
        
        # Step 1: Create content files
        print(f"\n📝 Step 1: Creating category content files...")
        content_files = self.create_category_content_files()
        
        # Step 2: Generate samples
        print(f"\n🤖 Step 2: Generating balanced samples...")
        generated_files = self.generate_balanced_samples(content_files, samples_per_category)
        
        if not generated_files:
            print("❌ No samples generated")
            return None
        
        # Step 3: Combine into balanced dataset
        print(f"\n🔄 Step 3: Combining into balanced dataset...")
        balanced_file = self.combine_balanced_dataset(generated_files)
        
        print(f"\n🎉 BALANCED DATASET CREATION COMPLETE!")
        print(f"   ✅ Perfect for LayerMatrix high-accuracy training")
        print(f"   ✅ Even labels across all categories")
        print(f"   ✅ Ready for 80%+ accuracy results!")
        
        return balanced_file


def main():
    """Main function to create balanced datasets."""
    creator = BalancedDatasetCreator()
    
    # Create balanced dataset with 150 samples per category
    # Total: ~900 samples across 6 categories = perfect balance
    balanced_file = creator.create_complete_balanced_dataset(samples_per_category=150)
    
    if balanced_file:
        print(f"\n🚀 Ready to train LayerMatrix with balanced data!")
        print(f"   Next: python3 train_balanced_layermatrix.py {balanced_file}")
    else:
        print(f"\n❌ Dataset creation failed")


if __name__ == "__main__":
    main()
