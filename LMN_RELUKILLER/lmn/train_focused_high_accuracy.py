#!/usr/bin/env python3
"""
Focused High-Accuracy LayerMatrix Training

Target: 80%+ accuracy with focused domain training
Strategy: Single domain, more data, optimized training
Expected: 5-15 minutes, 80-90% accuracy 🎯
"""

import sys
import os
import numpy as np
import json
import time
from pathlib import Path
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

# Import our modules
sys.path.insert(0, '.')

import importlib.util
spec = importlib.util.spec_from_file_location("MassiveLayerMatrix", "lmn/MassiveLayerMatrix.py")
massive_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(massive_module)
MassiveLayerMatrix = massive_module.MassiveLayerMatrix

class FocusedHighAccuracyTrainer:
    """Focused trainer optimized for high accuracy on specific domains."""
    
    def __init__(self, target_domain="historical_events"):
        self.target_domain = target_domain
        
        # Optimized vectorizer for better semantic understanding
        self.vectorizer = TfidfVectorizer(
            max_features=2048,  # More features for better representation
            stop_words='english',
            ngram_range=(1, 3),  # Include trigrams for better context
            min_df=2,
            max_df=0.85,
            sublinear_tf=True,  # Better for text classification
            norm='l2'
        )
        
        self.texts = []
        self.labels = []
        self.metadata = []
        
        print("🎯 Focused High-Accuracy Trainer initialized!")
        print(f"   Target domain: {target_domain}")
        print("   Goal: 80%+ accuracy with optimized training")
    
    def load_focused_historical_data(self, min_samples: int = 1500):
        """Load focused historical data for high accuracy."""
        print(f"\n🏛️  Loading focused historical data (target: {min_samples}+ samples)...")
        
        historical_dir = Path("historical_datasets")
        if not historical_dir.exists():
            print(f"❌ Historical directory not found: {historical_dir}")
            return 0
        
        loaded = 0
        category_counts = {}
        
        for hist_file in historical_dir.glob("*.json"):
            try:
                with open(hist_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                training_data = data.get('training_data', [])
                print(f"   📚 Processing {hist_file.name}: {len(training_data)} available")
                
                for item in training_data:
                    if loaded >= min_samples * 2:  # Load extra for better selection
                        break
                    
                    # Extract meaningful text
                    question = item.get('question', '').strip()
                    answer = item.get('answer', '').strip()
                    category = item.get('category', 'unknown')
                    year = item.get('year')
                    confidence = item.get('confidence', 0.5)
                    
                    # Quality filters for high accuracy
                    if len(question) < 20 or len(answer) < 30:
                        continue
                    if confidence < 0.3:
                        continue
                    
                    # Create rich text representation
                    combined_text = f"{question} {answer}"
                    
                    # Create focused labels based on category and time period
                    if year and year > 0:
                        if year >= 1900:
                            time_label = "modern"
                        elif year >= 1500:
                            time_label = "early_modern"
                        elif year >= 1000:
                            time_label = "medieval"
                        else:
                            time_label = "ancient"
                    else:
                        time_label = "unknown_period"
                    
                    # Combine category and time for rich classification
                    focused_label = f"{category}_{time_label}"
                    
                    self.texts.append(combined_text)
                    self.labels.append(focused_label)
                    self.metadata.append({
                        'category': category,
                        'year': year,
                        'confidence': confidence,
                        'source_file': hist_file.name
                    })
                    
                    category_counts[focused_label] = category_counts.get(focused_label, 0) + 1
                    loaded += 1
                
            except Exception as e:
                print(f"⚠️  Error loading {hist_file}: {e}")
                continue
        
        print(f"✅ Loaded {loaded} focused historical samples")
        print(f"📊 Categories found: {len(category_counts)}")
        
        # Show top categories
        top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:8]
        for cat, count in top_categories:
            print(f"   {cat}: {count} samples")
        
        return loaded
    
    def balance_dataset(self, min_samples_per_class: int = 50):
        """Balance the dataset for better training."""
        print(f"\n⚖️  Balancing dataset (min {min_samples_per_class} per class)...")
        
        # Count samples per label
        label_counts = {}
        for label in self.labels:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        # Filter out classes with too few samples
        valid_labels = {label: count for label, count in label_counts.items() 
                       if count >= min_samples_per_class}
        
        print(f"   Classes with enough samples: {len(valid_labels)}")
        
        # Create balanced dataset
        balanced_texts = []
        balanced_labels = []
        balanced_metadata = []
        
        for label in valid_labels:
            # Get all samples for this label
            label_indices = [i for i, l in enumerate(self.labels) if l == label]
            
            # Sample up to a reasonable maximum per class
            max_per_class = min(200, len(label_indices))
            if len(label_indices) > max_per_class:
                selected_indices = np.random.choice(label_indices, max_per_class, replace=False)
            else:
                selected_indices = label_indices
            
            for idx in selected_indices:
                balanced_texts.append(self.texts[idx])
                balanced_labels.append(self.labels[idx])
                balanced_metadata.append(self.metadata[idx])
        
        # Update datasets
        self.texts = balanced_texts
        self.labels = balanced_labels
        self.metadata = balanced_metadata
        
        # Show final distribution
        final_counts = {}
        for label in self.labels:
            final_counts[label] = final_counts.get(label, 0) + 1
        
        print(f"✅ Balanced dataset created:")
        print(f"   Total samples: {len(self.texts)}")
        print(f"   Classes: {len(final_counts)}")
        print(f"   Samples per class: {min(final_counts.values())}-{max(final_counts.values())}")
        
        return len(self.texts)
    
    def prepare_optimized_features(self):
        """Prepare optimized features for high accuracy."""
        print(f"\n🔤 Creating optimized text features...")
        
        if len(self.texts) == 0:
            print("❌ No data to process!")
            return None, None
        
        # Vectorize with optimized settings
        X = self.vectorizer.fit_transform(self.texts).toarray().astype(np.float32)
        
        # Encode labels
        label_encoder = LabelEncoder()
        y = label_encoder.fit_transform(self.labels)
        
        print(f"✅ Features prepared:")
        print(f"   Samples: {X.shape[0]:,}")
        print(f"   Features: {X.shape[1]:,}")
        print(f"   Classes: {len(label_encoder.classes_)}")
        print(f"   Feature density: {np.mean(X > 0):.3f}")
        
        return X, y, label_encoder
    
    def train_high_accuracy_model(self, X, y, label_encoder):
        """Train LayerMatrix optimized for high accuracy."""
        
        # Split with stratification for balanced training
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features for better convergence
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"\n🧠 Creating high-accuracy LayerMatrix model...")
        print(f"   Training samples: {len(X_train):,}")
        print(f"   Test samples: {len(X_test):,}")
        print(f"   Features: {X.shape[1]:,}")
        print(f"   Classes: {len(label_encoder.classes_)}")
        
        # Optimized model for high accuracy
        model = MassiveLayerMatrix(
            hidden_layers=[1024, 512, 256],  # Larger for better capacity
            accumulation_ratio=0.2,          # Optimal for text classification
            learning_rate=3e-4,              # Conservative for stability
            micro_batch_size=16,
            gradient_accumulation_steps=16,   # Larger effective batch
            max_memory_gb=8.0,
            use_mixed_precision=True,
            adaptive_accumulation=True,
            layer_norm=True,
            dropout_rate=0.2,                # Regularization for generalization
            weight_decay=1e-4,
            warmup_steps=100                 # Gradual learning rate warmup
        )
        
        print(f"🎯 Model created: {model._estimate_parameters():,} parameters")
        
        # Cross-validation for robust accuracy estimate
        print(f"\n🔄 Running cross-validation for robust evaluation...")
        cv_scores = []
        
        kf = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
        for fold, (train_idx, val_idx) in enumerate(kf.split(X_train_scaled, y_train)):
            print(f"   Fold {fold + 1}/3...")
            
            X_fold_train, X_fold_val = X_train_scaled[train_idx], X_train_scaled[val_idx]
            y_fold_train, y_fold_val = y_train[train_idx], y_train[val_idx]
            
            # Create fresh model for each fold
            fold_model = MassiveLayerMatrix(
                hidden_layers=[1024, 512, 256],
                accumulation_ratio=0.2,
                learning_rate=3e-4,
                micro_batch_size=16,
                gradient_accumulation_steps=16,
                max_memory_gb=8.0,
                use_mixed_precision=True,
                adaptive_accumulation=True,
                layer_norm=True,
                dropout_rate=0.2,
                weight_decay=1e-4,
                warmup_steps=100
            )
            
            # Train with more epochs for better convergence
            fold_model.fit(X_fold_train, y_fold_train, epochs=12, verbose=False)
            
            # Evaluate
            val_predictions = fold_model.predict(X_fold_val)
            val_accuracy = accuracy_score(y_fold_val, val_predictions)
            cv_scores.append(val_accuracy)
            
            print(f"      Fold {fold + 1} accuracy: {val_accuracy:.3f}")
            fold_model.cleanup()
        
        cv_mean = np.mean(cv_scores)
        cv_std = np.std(cv_scores)
        print(f"   Cross-validation: {cv_mean:.3f} ± {cv_std:.3f}")
        
        # Train final model on full training set
        print(f"\n🚀 Training final high-accuracy model...")
        start_time = time.time()
        
        # Extended training for high accuracy
        model.fit(X_train_scaled, y_train, epochs=15, verbose=True)
        
        train_time = time.time() - start_time
        
        # Final evaluation
        print(f"\n📊 Final evaluation...")
        test_predictions = model.predict(X_test_scaled)
        test_accuracy = accuracy_score(y_test, test_predictions)
        
        # Detailed classification report
        print(f"\n📋 Classification Report:")
        class_names = [name[:20] for name in label_encoder.classes_]  # Truncate for display
        print(classification_report(y_test, test_predictions, target_names=class_names, zero_division=0))
        
        print(f"\n🏆 HIGH-ACCURACY TRAINING COMPLETE!")
        print(f"   Cross-validation: {cv_mean:.1%} ± {cv_std:.1%}")
        print(f"   Final test accuracy: {test_accuracy:.1%}")
        print(f"   Training time: {train_time/60:.1f} minutes")
        print(f"   Parameters: {model._estimate_parameters():,}")
        
        model.cleanup()
        return test_accuracy, cv_mean, train_time
    
    def run_focused_training(self):
        """Run the complete focused high-accuracy training."""
        print("🎯 FOCUSED HIGH-ACCURACY LAYERMATRIX TRAINING")
        print("=" * 70)
        print("Goal: 80%+ accuracy with optimized domain-focused training")
        
        # Load focused data
        loaded_count = self.load_focused_historical_data(min_samples=1500)
        
        if loaded_count == 0:
            print("❌ No data loaded. Check data sources.")
            return 0.0
        
        # Balance dataset
        balanced_count = self.balance_dataset(min_samples_per_class=50)
        
        if balanced_count == 0:
            print("❌ No balanced data created.")
            return 0.0
        
        # Prepare features
        X, y, label_encoder = self.prepare_optimized_features()
        
        if X is None:
            print("❌ Feature preparation failed.")
            return 0.0
        
        # Train high-accuracy model
        test_accuracy, cv_accuracy, train_time = self.train_high_accuracy_model(X, y, label_encoder)
        
        print(f"\n🎉 FOCUSED TRAINING SUCCESS!")
        print(f"   ✅ Domain: {self.target_domain}")
        print(f"   ✅ Samples: {balanced_count:,}")
        print(f"   ✅ CV Accuracy: {cv_accuracy:.1%}")
        print(f"   ✅ Test Accuracy: {test_accuracy:.1%}")
        print(f"   ✅ Training time: {train_time/60:.1f} minutes")
        
        return test_accuracy


def main():
    """Main focused training function."""
    trainer = FocusedHighAccuracyTrainer(target_domain="historical_events")
    accuracy = trainer.run_focused_training()
    
    if accuracy >= 0.8:
        print(f"\n🏆 EXCELLENT! TARGET ACHIEVED! ({accuracy:.1%})")
        print("   Ready for production deployment! 🚀")
    elif accuracy >= 0.7:
        print(f"\n👍 VERY GOOD! Close to target! ({accuracy:.1%})")
        print("   Minor tuning could reach 80%+")
    elif accuracy >= 0.5:
        print(f"\n📈 GOOD PROGRESS! ({accuracy:.1%})")
        print("   More data or training could improve further")
    else:
        print(f"\n🔧 NEEDS OPTIMIZATION ({accuracy:.1%})")
        print("   Check data quality and model parameters")
    
    print(f"\n🎯 Focused high-accuracy training complete!")


if __name__ == "__main__":
    main()
