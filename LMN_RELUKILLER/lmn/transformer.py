import torch
import torch.nn as nn
import torch.nn.functional as F
import math

# --- Component 1: The LoRA Plugin Layer ---
class LoRALayer(nn.Module):
    """
    A Low-Rank Adaptation (LoRA) layer that wraps a standard nn.Linear layer.
    """
    def __init__(self, original_layer: nn.Linear, rank: int, alpha: float):
        super().__init__()
        self.original_layer = original_layer
        
        in_features = original_layer.in_features
        out_features = original_layer.out_features

        # New, trainable LoRA matrices A and B
        self.lora_A = nn.Parameter(torch.randn(in_features, rank))
        self.lora_B = nn.Parameter(torch.zeros(rank, out_features))
        
        # Scaling factor
        self.scaling = alpha / rank

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Original path (frozen)
        original_output = self.original_layer(x)
        
        # LoRA path (trainable)
        lora_output = (x @ self.lora_A @ self.lora_B) * self.scaling
        
        return original_output + lora_output

# --- Component 2: The Core Model Architecture ---
class LayerMatrix(nn.Module):
    def __init__(self, d_model: int, d_ff: int, accumulation_ratio: float = 0.4):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.accumulation_ratio = accumulation_ratio

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        global_context = torch.mean(x, dim=1, keepdim=True)
        accumulated_x = x + global_context * self.accumulation_ratio
        return self.linear2(torch.tanh(self.linear1(accumulated_x)))

class ArkonaTransformerLayer(nn.Module):
    def __init__(self, d_model: int, nhead: int, d_ff: int = 2048, dropout: float = 0.1, accumulation_ratio: float = 0.4):
        super().__init__()
        self.self_attn = nn.MultiheadAttention(d_model, nhead, dropout=dropout, batch_first=True)
        self.layermatrix_ffn = LayerMatrix(d_model, d_ff, accumulation_ratio)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, src: torch.Tensor) -> torch.Tensor:
        attn_output, _ = self.self_attn(src, src, src)
        src = src + self.dropout(attn_output)
        src = self.norm1(src)
        lm_output = self.layermatrix_ffn(src)
        src = src + self.dropout(lm_output)
        src = self.norm2(src)
        return src

class PositionalEncoding(nn.Module):
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, 1, d_model)
        pe[:, 0, 0::2] = torch.sin(position * div_term)
        pe[:, 0, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.transpose(0, 1))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x + self.pe[:, :x.size(1)]
        return self.dropout(x)

class ArkonaTransformerModel(nn.Module):
    def __init__(self, n_tokens: int, d_model: int, nhead: int, d_ff: int, n_layers: int, dropout: float = 0.1, accumulation_ratio: float = 0.4):
        super().__init__()
        self.d_model = d_model
        self.embedding = nn.Embedding(n_tokens, d_model)
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        encoder_layers = nn.ModuleList([ArkonaTransformerLayer(d_model, nhead, d_ff, dropout, accumulation_ratio) for _ in range(n_layers)])
        self.transformer_encoder = nn.Sequential(*encoder_layers)
        self.output_layer = nn.Linear(d_model, n_tokens)

    def forward(self, src: torch.Tensor) -> torch.Tensor:
        src = self.embedding(src) * math.sqrt(self.d_model)
        src = self.pos_encoder(src)
        output = self.transformer_encoder(src)
        output = self.output_layer(output)
        return output

# --- Component 3: The LoRA Plugin Utilities ---
def apply_lora_to_model(model: nn.Module, rank: int, alpha: float):
    print("\nApplying LoRA plugin to the Arkona Transformer...")
    for name, module in model.named_modules():
        # Target all Linear layers for replacement, except the final output layer
        if isinstance(module, nn.Linear) and 'output_layer' not in name:
            path_parts = name.split('.')
            parent = model
            for part in path_parts[:-1]:
                parent = getattr(parent, part)
            
            original_layer = getattr(parent, path_parts[-1])
            setattr(parent, path_parts[-1], LoRALayer(original_layer, rank, alpha))
            print(f"  - ✅ Replaced '{name}' with LoRALayer.")
    return model

def setup_lora_training(model: nn.Module):
    """
    Freezes all base model parameters and unfreezes only LoRA parameters.
    """
    total_params = sum(p.numel() for p in model.parameters())
    
    # Freeze all parameters by default
    for param in model.parameters():
        param.requires_grad = False
    
    # Unfreeze only the parameters of our LoRALayer adapters
    for module in model.modules():
        if isinstance(module, LoRALayer):
            for param in module.parameters():
                param.requires_grad = True
            
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"\nLoRA training setup complete.")
    print(f"  - Total Parameters:     {total_params:,}")
    print(f"  - Trainable Parameters: {trainable_params:,} ({100 * trainable_params / total_params:.4f}%)")
    return model


if __name__ == '__main__':
    # Standard Model Hyperparameters
    VOCAB_SIZE, D_MODEL, N_HEAD, D_FF, N_LAYERS = 10000, 256, 8, 1024, 4

    # 1. Instantiate the base Arkona Transformer model
    model = ArkonaTransformerModel(VOCAB_SIZE, D_MODEL, N_HEAD, D_FF, N_LAYERS)
    print("--- BASE ARKONA TRANSFORMER ---")
    print(f"Total Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 2. Apply the LoRA Plugin
    LORA_RANK = 8
    LORA_ALPHA = 16
    model_with_lora = apply_lora_to_model(model, rank=LORA_RANK, alpha=LORA_ALPHA)
    
    # 3. Set up for efficient training (this time it will work!)
    lora_trainable_model = setup_lora_training(model_with_lora)
    
    print("\n✅ The ARKONA Transformer is now LoRA-enabled and ready for hyper-efficient fine-tuning.")