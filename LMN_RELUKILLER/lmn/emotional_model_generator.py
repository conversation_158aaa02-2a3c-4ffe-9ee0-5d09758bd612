#!/usr/bin/env python3
"""
Emotional Model Sample Generator for ARKONA

Integrates the progenitor's emotional framework to generate training samples
for emotional intelligence, empathy, and psychological understanding.

Features:
- RULER framework integration (Recognize, Understand, Label, Express, Regulate)
- Corporate state emotional tracking
- Prefrontal cortex mood modeling
- Empathy and emotional response training
- Psychological state analysis
"""

import sys
import json
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass

# Add progenitor path for imports
PROGENITOR_PATH = Path("/Users/<USER>/Desktop/arkona_train/progenitor")
sys.path.insert(0, str(PROGENITOR_PATH))

# Import emotional framework
try:
    from psyche.emotional_map import EMOTION_MAP
except ImportError:
    # Fallback emotional map if import fails
    EMOTION_MAP = {
        "ANGER": {
            "meaning": "A boundary has been crossed or something is unfair.",
            "need": "To establish a boundary, defend a principle, or assert oneself.",
            "prompt_keyword": "Assertiveness"
        },
        "FEAR": {
            "meaning": "There is a sense of immediate, overwhelming danger or a threat to safety.",
            "need": "To find safety, increase security, and use grounding techniques.",
            "prompt_keyword": "Safety"
        },
        "HAPPINESS": {
            "meaning": "Making reasonable progress toward a goal; something is good for oneself.",
            "need": "To be aware of the progress, enjoy the moment, and celebrate.",
            "prompt_keyword": "Celebration"
        },
        "SADNESS": {
            "meaning": "An irrevocable loss has been experienced; something important is missing.",
            "need": "To receive soothing, comfort, and be allowed space to process.",
            "prompt_keyword": "Comfort"
        },
        "SURPRISE": {
            "meaning": "Something unexpected or astonishing has occurred, while remaining relatively safe.",
            "need": "To feel safe and understand the new situation.",
            "prompt_keyword": "Understanding"
        },
        "DISGUST": {
            "meaning": "Confronted with something indigestible or damaging to wellbeing.",
            "need": "To expel, reject, or create distance from the offending object.",
            "prompt_keyword": "Distance"
        }
    }

@dataclass
class EmotionalState:
    """Represents an emotional state with context and needs."""
    emotion: str
    intensity: float  # 0.0 to 1.0
    context: str
    triggers: List[str]
    needs: List[str]
    coping_strategies: List[str]

class EmotionalModelGenerator:
    """Generate training samples for emotional intelligence and empathy."""
    
    def __init__(self):
        self.emotion_map = EMOTION_MAP
        self.output_dir = Path("emotional_training_data")
        self.output_dir.mkdir(exist_ok=True)
        
        # Emotional scenarios for training
        self.scenarios = {
            "workplace": [
                "receiving criticism from a supervisor",
                "being passed over for a promotion",
                "completing a challenging project successfully",
                "dealing with a difficult colleague",
                "facing a tight deadline",
                "receiving unexpected praise",
                "making a mistake in front of the team",
                "learning new skills"
            ],
            "learning": [
                "struggling with complex concepts",
                "having a breakthrough moment",
                "receiving feedback on work",
                "comparing progress to others",
                "facing imposter syndrome",
                "discovering new interests",
                "overcoming learning obstacles",
                "achieving learning goals"
            ],
            "social": [
                "feeling misunderstood by others",
                "forming new relationships",
                "experiencing conflict with friends",
                "feeling lonely or isolated",
                "being included in group activities",
                "helping someone in need",
                "receiving support during difficult times",
                "celebrating achievements with others"
            ],
            "personal_growth": [
                "questioning one's purpose",
                "setting new life goals",
                "overcoming personal fears",
                "developing self-confidence",
                "dealing with uncertainty",
                "embracing change",
                "reflecting on past experiences",
                "planning for the future"
            ]
        }
        
        print("🧠 Emotional Model Generator initialized!")
        print(f"   Emotions: {list(self.emotion_map.keys())}")
        print(f"   Scenarios: {sum(len(v) for v in self.scenarios.values())}")
    
    def generate_emotional_state(self) -> EmotionalState:
        """Generate a random emotional state with context."""
        emotion = random.choice(list(self.emotion_map.keys()))
        intensity = random.uniform(0.3, 1.0)
        
        # Select scenario context
        category = random.choice(list(self.scenarios.keys()))
        context = random.choice(self.scenarios[category])
        
        # Generate triggers based on emotion and context
        triggers = self._generate_triggers(emotion, context)
        
        # Generate needs based on emotion map
        emotion_info = self.emotion_map[emotion]
        needs = [emotion_info["need"]]
        
        # Generate coping strategies
        coping_strategies = self._generate_coping_strategies(emotion, intensity)
        
        return EmotionalState(
            emotion=emotion,
            intensity=intensity,
            context=context,
            triggers=triggers,
            needs=needs,
            coping_strategies=coping_strategies
        )
    
    def _generate_triggers(self, emotion: str, context: str) -> List[str]:
        """Generate contextual triggers for the emotion."""
        trigger_templates = {
            "ANGER": [
                f"Feeling unfairly treated while {context}",
                f"Having boundaries violated during {context}",
                f"Experiencing injustice in {context}"
            ],
            "FEAR": [
                f"Feeling threatened or unsafe while {context}",
                f"Anticipating negative outcomes from {context}",
                f"Feeling overwhelmed by {context}"
            ],
            "HAPPINESS": [
                f"Achieving success in {context}",
                f"Receiving positive feedback about {context}",
                f"Making progress with {context}"
            ],
            "SADNESS": [
                f"Experiencing loss related to {context}",
                f"Feeling disappointed about {context}",
                f"Missing something important in {context}"
            ],
            "SURPRISE": [
                f"Encountering unexpected events during {context}",
                f"Discovering new information about {context}",
                f"Experiencing sudden changes in {context}"
            ],
            "DISGUST": [
                f"Encountering something repulsive in {context}",
                f"Feeling morally opposed to aspects of {context}",
                f"Rejecting harmful elements in {context}"
            ]
        }
        
        return trigger_templates.get(emotion, [f"General trigger related to {context}"])
    
    def _generate_coping_strategies(self, emotion: str, intensity: float) -> List[str]:
        """Generate appropriate coping strategies."""
        strategies = {
            "ANGER": [
                "Take deep breaths and count to ten",
                "Express feelings assertively but respectfully",
                "Take a break to cool down",
                "Focus on problem-solving rather than blame"
            ],
            "FEAR": [
                "Practice grounding techniques",
                "Seek support from trusted individuals",
                "Break down overwhelming tasks into smaller steps",
                "Use positive self-talk and affirmations"
            ],
            "HAPPINESS": [
                "Savor the positive moment",
                "Share the joy with others",
                "Reflect on what led to this success",
                "Use this energy for future goals"
            ],
            "SADNESS": [
                "Allow yourself to feel and process the emotion",
                "Seek comfort from supportive people",
                "Engage in self-care activities",
                "Consider professional support if needed"
            ],
            "SURPRISE": [
                "Take time to process the new information",
                "Ask questions to better understand",
                "Adapt plans based on new circumstances",
                "Stay open to learning and growth"
            ],
            "DISGUST": [
                "Create healthy boundaries",
                "Remove yourself from harmful situations",
                "Focus on values and principles",
                "Seek environments that align with your values"
            ]
        }
        
        base_strategies = strategies.get(emotion, ["Practice mindfulness and self-awareness"])
        
        # Add intensity-based strategies
        if intensity > 0.7:
            base_strategies.extend([
                "Seek immediate support",
                "Use emergency coping techniques",
                "Consider professional intervention"
            ])
        
        return random.sample(base_strategies, min(3, len(base_strategies)))
    
    def create_emotional_qa_pairs(self, emotional_state: EmotionalState) -> List[Dict]:
        """Create Q&A pairs for emotional intelligence training."""
        qa_pairs = []
        
        emotion_info = self.emotion_map[emotional_state.emotion]
        
        # Recognition and labeling questions
        qa_pairs.extend([
            {
                "question": f"What emotion might someone experience when {emotional_state.context}?",
                "answer": f"They might experience {emotional_state.emotion.lower()}, which typically means {emotion_info['meaning'].lower()}",
                "category": "emotion_recognition",
                "emotion": emotional_state.emotion,
                "intensity": emotional_state.intensity
            },
            {
                "question": f"How can you tell if someone is feeling {emotional_state.emotion.lower()}?",
                "answer": f"Signs of {emotional_state.emotion.lower()} include {', '.join(emotional_state.triggers[:2])}. The underlying meaning is: {emotion_info['meaning']}",
                "category": "emotion_identification",
                "emotion": emotional_state.emotion,
                "intensity": emotional_state.intensity
            }
        ])
        
        # Understanding and empathy questions
        qa_pairs.extend([
            {
                "question": f"What does someone feeling {emotional_state.emotion.lower()} need most?",
                "answer": f"Someone experiencing {emotional_state.emotion.lower()} needs: {emotion_info['need']}",
                "category": "emotional_needs",
                "emotion": emotional_state.emotion,
                "intensity": emotional_state.intensity
            },
            {
                "question": f"How can you help someone who is {emotional_state.context} and feeling {emotional_state.emotion.lower()}?",
                "answer": f"You can help by: {', '.join(emotional_state.coping_strategies[:2])}. Remember that {emotion_info['meaning'].lower()}",
                "category": "emotional_support",
                "emotion": emotional_state.emotion,
                "intensity": emotional_state.intensity
            }
        ])
        
        # Regulation and expression questions
        qa_pairs.extend([
            {
                "question": f"What are healthy ways to cope with {emotional_state.emotion.lower()}?",
                "answer": f"Healthy coping strategies include: {', '.join(emotional_state.coping_strategies)}",
                "category": "emotion_regulation",
                "emotion": emotional_state.emotion,
                "intensity": emotional_state.intensity
            },
            {
                "question": f"How might {emotional_state.emotion.lower()} be expressed constructively?",
                "answer": f"Constructive expression involves {emotion_info['prompt_keyword'].lower()}: {emotion_info['need']}",
                "category": "emotion_expression",
                "emotion": emotional_state.emotion,
                "intensity": emotional_state.intensity
            }
        ])
        
        return qa_pairs
    
    def generate_emotional_training_dataset(self, num_samples: int = 500) -> Dict:
        """Generate a complete emotional intelligence training dataset."""
        print(f"\n🧠 Generating emotional intelligence dataset ({num_samples} samples)...")
        
        all_qa_pairs = []
        emotion_distribution = {emotion: 0 for emotion in self.emotion_map.keys()}
        
        for i in range(num_samples):
            # Generate emotional state
            emotional_state = self.generate_emotional_state()
            emotion_distribution[emotional_state.emotion] += 1
            
            # Create Q&A pairs
            qa_pairs = self.create_emotional_qa_pairs(emotional_state)
            all_qa_pairs.extend(qa_pairs)
            
            if (i + 1) % 100 == 0:
                print(f"   Generated {i + 1}/{num_samples} emotional states...")
        
        # Create dataset
        dataset = {
            "metadata": {
                "dataset_name": "arkona_emotional_intelligence",
                "created_at": time.time(),
                "total_samples": len(all_qa_pairs),
                "emotional_states_generated": num_samples,
                "emotion_distribution": emotion_distribution,
                "categories": ["emotion_recognition", "emotion_identification", "emotional_needs", 
                             "emotional_support", "emotion_regulation", "emotion_expression"],
                "format": "layermatrix_emotional_training"
            },
            "training_data": all_qa_pairs
        }
        
        print(f"✅ Generated {len(all_qa_pairs)} emotional Q&A pairs")
        print(f"📊 Emotion distribution: {emotion_distribution}")
        
        return dataset
    
    def save_emotional_dataset(self, dataset: Dict, filename: str = None) -> Path:
        """Save the emotional training dataset."""
        if filename is None:
            timestamp = int(time.time())
            filename = f"emotional_intelligence_{len(dataset['training_data'])}_samples_{timestamp}.json"
        
        output_file = self.output_dir / filename
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved emotional dataset: {output_file}")
        return output_file


def main():
    """Generate emotional intelligence training data for ARKONA."""
    print("🧠💝 ARKONA EMOTIONAL INTELLIGENCE GENERATOR")
    print("=" * 60)
    print("Creating empathy and emotional understanding training data")
    
    generator = EmotionalModelGenerator()
    
    # Generate emotional intelligence dataset
    dataset = generator.generate_emotional_training_dataset(num_samples=300)
    
    # Save dataset
    output_file = generator.save_emotional_dataset(dataset)
    
    print(f"\n🎉 EMOTIONAL INTELLIGENCE DATASET CREATED!")
    print(f"   File: {output_file}")
    print(f"   Samples: {len(dataset['training_data']):,}")
    print(f"   Emotions: {len(dataset['metadata']['emotion_distribution'])}")
    print(f"   Ready for LayerMatrix training! 🚀")
    
    print(f"\n💝 ARKONA will now understand:")
    print("   ✅ Emotional recognition and labeling")
    print("   ✅ Empathy and emotional needs")
    print("   ✅ Emotional support strategies")
    print("   ✅ Healthy emotion regulation")
    print("   ✅ Constructive emotional expression")


if __name__ == "__main__":
    main()
