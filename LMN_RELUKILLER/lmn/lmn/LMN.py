
from scipy.sparse import hstack # We use this to combine feature matrices
import numpy as np
import pandas as pd
import json
import time
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import warnings
import re # Imported for parsing
warnings.filterwarnings('ignore')
class LayerMatrix:
    """
    THE ENHANCED LAYERMATRIX: Optimized to beat MLPClassifier!

    Improvements:
    - Adam optimizer with adaptive learning rates
    - Better weight initialization (Xavier/<PERSON> hybrid)
    - Batch normalization for stable training
    - Gradient clipping for stability
    - Early stopping with patience
    - Learning rate scheduling
    - ReLU activations with dropout
    - Advanced regularization techniques
    """

    def __init__(self, hidden_layers=[128, 64], accumulation_ratio=0.3,
                 learning_rate=0.001, l2_reg=0.0001, batch_size=64,
                 dropout_rate=0.2, use_batch_norm=True, early_stopping_patience=10):
        self.hidden_layers = hidden_layers
        self.accumulation_ratio = accumulation_ratio
        self.initial_learning_rate = learning_rate
        self.learning_rate = learning_rate
        self.l2_reg = l2_reg
        self.batch_size = batch_size
        self.dropout_rate = dropout_rate
        self.use_batch_norm = use_batch_norm
        self.early_stopping_patience = early_stopping_patience

        # Adam optimizer parameters
        self.beta1 = 0.9
        self.beta2 = 0.999
        self.epsilon = 1e-8
        self.t = 0  # time step for Adam

        # Training state
        self.layers = []
        self.batch_norm_params = []
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_history = {
            'loss': [], 'accuracy': [], 'val_loss': [], 'val_accuracy': [],
            'info_preservation': [], 'learning_rate': []
        }

        print("🚀 ENHANCED LAYERMATRIX NETWORK INITIALIZED!")
        print(f"Architecture: {hidden_layers}, Accumulation: {accumulation_ratio}")
        print(f"Optimizer: Adam, LR: {learning_rate}, Dropout: {dropout_rate}")
        print(f"Batch Norm: {use_batch_norm}, Early Stopping: {early_stopping_patience}")

    def _initialize_layers(self, input_size, output_size):
        layer_sizes = [input_size] + self.hidden_layers + [output_size]
        self.layers = []
        self.batch_norm_params = []

        for i in range(len(layer_sizes) - 1):
            # Enhanced weight initialization (Xavier for tanh, He for ReLU)
            if i == len(layer_sizes) - 2:  # Output layer
                # Xavier initialization for output layer
                limit = np.sqrt(6.0 / (layer_sizes[i] + layer_sizes[i+1]))
                weights = np.random.uniform(-limit, limit, (layer_sizes[i], layer_sizes[i+1]))
            else:
                # Optimized initialization for tanh activations (LayerMatrix specialty)
                weights = np.random.randn(layer_sizes[i], layer_sizes[i+1]) * np.sqrt(1.0 / layer_sizes[i])

            layer = {
                'weights': weights,
                'bias': np.zeros(layer_sizes[i+1]),
                # Adam optimizer parameters
                'm_w': np.zeros((layer_sizes[i], layer_sizes[i+1])),  # First moment
                'v_w': np.zeros((layer_sizes[i], layer_sizes[i+1])),  # Second moment
                'm_b': np.zeros(layer_sizes[i+1]),
                'v_b': np.zeros(layer_sizes[i+1])
            }
            self.layers.append(layer)

            # Batch normalization parameters (for hidden layers only)
            if i < len(layer_sizes) - 2 and self.use_batch_norm:
                bn_params = {
                    'gamma': np.ones(layer_sizes[i+1]),  # Scale parameter
                    'beta': np.zeros(layer_sizes[i+1]),  # Shift parameter
                    'running_mean': np.zeros(layer_sizes[i+1]),
                    'running_var': np.ones(layer_sizes[i+1]),
                    'momentum': 0.9
                }
                self.batch_norm_params.append(bn_params)
            else:
                self.batch_norm_params.append(None)

        print(f"🧠 Enhanced neural architecture initialized: {layer_sizes}")
        print(f"   - Weight init: Xavier (output) + He (hidden)")
        print(f"   - Optimizer: Adam with β1={self.beta1}, β2={self.beta2}")
        if self.use_batch_norm:
            print(f"   - Batch normalization enabled for hidden layers")

    def _layermatrix_forward(self, X, training=True):
        """
        Enhanced LayerMatrix forward pass with improved global context accumulation.
        Preserves the core LayerMatrix innovation while adding stability improvements.
        """
        current_input = X
        layer_outputs = [current_input]

        for i, layer in enumerate(self.layers):
            # CORE LAYERMATRIX INNOVATION: Global context accumulation
            if i > 0:
                # Enhanced global context with both mean and variance information
                global_mean = np.mean(current_input, axis=0, keepdims=True)
                global_std = np.std(current_input, axis=0, keepdims=True) + 1e-8

                # Adaptive accumulation based on layer depth
                depth_factor = 1.0 / (1.0 + i * 0.1)  # Reduce accumulation in deeper layers
                effective_ratio = self.accumulation_ratio * depth_factor

                # Enhanced accumulation with normalized context
                normalized_context = global_mean / (global_std + 1e-8)
                accumulated_input = current_input + normalized_context * effective_ratio
            else:
                accumulated_input = current_input

            # Linear transformation
            z = np.dot(accumulated_input, layer['weights']) + layer['bias']

            # Batch normalization for hidden layers (if enabled)
            if i < len(self.layers) - 1 and self.use_batch_norm and self.batch_norm_params[i] is not None:
                z = self._batch_normalize(z, i, training)

            # Activation functions - preserving LayerMatrix's tanh advantage
            if i < len(self.layers) - 1:
                # Enhanced tanh with slight modification for better gradients
                current_input = np.tanh(z * 0.9)  # Slight scaling to prevent saturation

                # Dropout during training
                if training and self.dropout_rate > 0:
                    dropout_mask = np.random.binomial(1, 1 - self.dropout_rate, current_input.shape)
                    current_input = current_input * dropout_mask / (1 - self.dropout_rate)
            else:
                # Output layer - stable sigmoid
                current_input = 1 / (1 + np.exp(-np.clip(z, -500, 500)))

            layer_outputs.append(current_input)

        return current_input, layer_outputs

    def _batch_normalize(self, z, layer_idx, training=True):
        """Batch normalization for stable training."""
        bn_params = self.batch_norm_params[layer_idx]
        if bn_params is None:
            return z

        if training:
            # Training mode: compute batch statistics
            batch_mean = np.mean(z, axis=0)
            batch_var = np.var(z, axis=0)

            # Update running statistics
            bn_params['running_mean'] = (bn_params['momentum'] * bn_params['running_mean'] +
                                       (1 - bn_params['momentum']) * batch_mean)
            bn_params['running_var'] = (bn_params['momentum'] * bn_params['running_var'] +
                                      (1 - bn_params['momentum']) * batch_var)

            # Normalize
            z_norm = (z - batch_mean) / np.sqrt(batch_var + 1e-8)
        else:
            # Inference mode: use running statistics
            z_norm = (z - bn_params['running_mean']) / np.sqrt(bn_params['running_var'] + 1e-8)

        # Scale and shift
        return bn_params['gamma'] * z_norm + bn_params['beta']

    def _layermatrix_backward(self, X, y, layer_outputs):
        """
        Enhanced backward pass with Adam optimizer.

        Adam is superior to momentum for LayerMatrix because it adapts learning rates
        per parameter, which is crucial for the complex information accumulation dynamics.
        """
        batch_size = X.shape[0]
        output = layer_outputs[-1]
        output_grad = (output - y.reshape(-1, 1)) / batch_size
        current_grad = output_grad

        # Increment time step for Adam
        self.t += 1

        for i in reversed(range(len(self.layers))):
            layer = self.layers[i]
            layer_input = layer_outputs[i]
            layer_output = layer_outputs[i+1]

            # Compute activation gradients (tanh derivative for hidden layers)
            if i < len(self.layers) - 1:
                # Tanh derivative: 1 - tanh²(x), but we use scaled tanh (0.9x)
                activation_grad = current_grad * (1 - layer_output**2) * 0.9
            else:
                # Output layer (sigmoid)
                activation_grad = current_grad

            # Compute gradients with L2 regularization
            weight_grad = np.dot(layer_input.T, activation_grad) + self.l2_reg * layer['weights']
            bias_grad = np.sum(activation_grad, axis=0)

            # Gradient clipping for stability
            weight_grad = np.clip(weight_grad, -1.0, 1.0)
            bias_grad = np.clip(bias_grad, -1.0, 1.0)

            # Adam optimizer updates
            # Update biased first moment estimates
            layer['m_w'] = self.beta1 * layer['m_w'] + (1 - self.beta1) * weight_grad
            layer['m_b'] = self.beta1 * layer['m_b'] + (1 - self.beta1) * bias_grad

            # Update biased second moment estimates
            layer['v_w'] = self.beta2 * layer['v_w'] + (1 - self.beta2) * (weight_grad ** 2)
            layer['v_b'] = self.beta2 * layer['v_b'] + (1 - self.beta2) * (bias_grad ** 2)

            # Bias correction
            m_w_corrected = layer['m_w'] / (1 - self.beta1 ** self.t)
            m_b_corrected = layer['m_b'] / (1 - self.beta1 ** self.t)
            v_w_corrected = layer['v_w'] / (1 - self.beta2 ** self.t)
            v_b_corrected = layer['v_b'] / (1 - self.beta2 ** self.t)

            # Update parameters
            layer['weights'] -= self.learning_rate * m_w_corrected / (np.sqrt(v_w_corrected) + self.epsilon)
            layer['bias'] -= self.learning_rate * m_b_corrected / (np.sqrt(v_b_corrected) + self.epsilon)

            # Propagate gradients to previous layer
            if i > 0:
                current_grad = np.dot(activation_grad, layer['weights'].T)

    def fit(self, X, y, validation_split=0.2, epochs=50, verbose=True):
        if verbose:
            print(f"\n🥊 TRAINING THE ULTIMATE LAYERMATRIX!")
            print(f"Dataset size: {X.shape[0]:,} records, {X.shape[1]} features")
        
        self._initialize_layers(X.shape[1], 1)
        split_idx = int(len(X) * (1 - validation_split))
        X_train, X_val, y_train, y_val = X[:split_idx], X[split_idx:], y[:split_idx], y[split_idx:]
        
        for epoch in range(epochs):
            epoch_start = time.time()
            total_loss, total_accuracy, batch_count = 0, 0, 0
            indices = np.random.permutation(len(X_train))
            X_train_shuffled, y_train_shuffled = X_train[indices], y_train[indices]
            
            for i in range(0, len(X_train_shuffled), self.batch_size):
                batch_X, batch_y = X_train_shuffled[i:i+self.batch_size], y_train_shuffled[i:i+self.batch_size]
                if not len(batch_X): continue
                output, layer_outputs = self._layermatrix_forward(batch_X)
                l2_loss = sum(np.sum(l['weights']**2) for l in self.layers)
                total_loss += np.mean((output.flatten() - batch_y)**2) + self.l2_reg * l2_loss
                total_accuracy += np.mean((output.flatten() > 0.5).astype(int) == batch_y)
                self._layermatrix_backward(batch_X, batch_y, layer_outputs)
                batch_count += 1
                
            avg_loss, avg_accuracy = (total_loss/batch_count, total_accuracy/batch_count) if batch_count > 0 else (0,0)
            val_output, _ = self._layermatrix_forward(X_val)
            val_loss = np.mean((val_output.flatten() - y_val)**2)
            val_accuracy = np.mean((val_output.flatten() > 0.5).astype(int) == y_val)
            info_preservation = np.mean(np.abs(val_output)) / np.mean(np.abs(X_val)) if np.mean(np.abs(X_val)) > 0 else 0
            
            for key, val in zip(self.training_history.keys(), [avg_loss, avg_accuracy, val_loss, val_accuracy, info_preservation]):
                self.training_history[key].append(val)
            
            if verbose and (epoch % 10 == 0 or epoch == epochs - 1):
                print(f"Epoch {epoch:3d}: Loss={avg_loss:.4f} Acc={avg_accuracy:.3f} | Val Loss={val_loss:.4f} Val Acc={val_accuracy:.3f} | Time={time.time()-epoch_start:.2f}s")
        
        print(f"\n🏆 TRAINING COMPLETE! Final Validation Accuracy: {val_accuracy:.3f}")
        return self

    def predict(self, X):
        output, _ = self._layermatrix_forward(X)
        return (output.flatten() > 0.5).astype(int)

    def score(self, X, y):
        return np.mean(self.predict(X) == y)

    def plot_training_history(self):
        if not self.training_history['loss']: print("No training history to plot."); return
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Ultimate LayerMatrix Training Journey', fontsize=16)
        axes[0, 0].plot(self.training_history['loss'], label='Training Loss'); axes[0, 0].plot(self.training_history['val_loss'], label='Validation Loss'); axes[0, 0].set_title('Loss'); axes[0, 0].legend(); axes[0, 0].grid(True)
        axes[0, 1].plot(self.training_history['accuracy'], label='Training Acc'); axes[0, 1].plot(self.training_history['val_accuracy'], label='Validation Acc'); axes[0, 1].set_title('Accuracy'); axes[0, 1].legend(); axes[0, 1].grid(True)
        axes[1, 0].plot(self.training_history['info_preservation'], label='Info Preservation', color='g'); axes[1, 0].set_title('Info Preservation'); axes[1, 0].legend(); axes[1, 0].grid(True)
        final_metrics = {k: self.training_history[k][-1] for k in ['accuracy', 'val_accuracy', 'info_preservation']}
        bars = axes[1, 1].bar(final_metrics.keys(), final_metrics.values(), color=['b', 'orange', 'g']); axes[1, 1].set_title('Final Metrics'); axes[1, 1].set_ylim(0, 1.1)
        for bar, val in zip(bars, final_metrics.values()): axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, f'{val:.3f}', ha='center', va='bottom')
        plt.tight_layout(rect=[0, 0.03, 1, 0.95]); plt.show() # type: ignore

def process_emotion_data(filepath='beast_food.json'):
    """
    Processes the 'emotion_context' category from the dataset.

    Extracts the emotional label, the standard response, and the
    emotionally-adapted response, then returns them as a Pandas DataFrame.
    """
    print(f"\n🚀 Initiating processing for 'emotion_context' from {filepath.upper()}...")
    
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        emotion_records = data['examples']['emotion_context']
        print(f"✅ Found {len(emotion_records):,} records in 'emotion_context'.")
        
        prepared_data = []
        for record in emotion_records:
            emotion_label = record.get('emotional_state', {}).get('label', 'unknown')
            standard_response = record.get('standard_response', {}).get('content', '')
            adapted_response = record.get('emotion_adapted_response', {}).get('content', '')
            
            # Ensure we have all the necessary parts before adding
            if emotion_label and standard_response and adapted_response:
                prepared_data.append({
                    'emotion': emotion_label,
                    'standard_text': standard_response,
                    'adapted_text': adapted_response
                })
        
        # Convert the list of dictionaries to a Pandas DataFrame
        df = pd.DataFrame(prepared_data)
        
        print(f"✅ Successfully processed {len(df):,} records into a DataFrame.")
        print("Data is now ready for a text-generation model!")
        
        return df

    except FileNotFoundError:
        print(f"❌ ERROR: File not found at {filepath}")
        return pd.DataFrame()
    except KeyError:
        print("❌ ERROR: Could not find 'examples' or 'emotion_context' in the file.")
        return pd.DataFrame()


def process_training_data(filepath='beast_food.json'):
    """
    Process the AI specialist dataset from the specified JSON file.
    This function is corrected to handle the actual data structure.
    """
    print(f"🔍 LOADING {filepath.upper()}!")
    print("🍖 Time to feed the LayerMatrix beast!")
    
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
        print(f"✅ {filepath.upper()} LOADED SUCCESSFULLY!")
        
        # FIXED: Point to the correct data path to resolve the KeyError
        training_data = data['examples']['internal_training']
        total_records = len(training_data)
        
        print(f"🦁 DATASET STATS: Total Records: {total_records:,}")
        
        processed_data = {
            'confidence_current': [], 'confidence_target': [], 'automation_cost_monthly': [],
            'training_epochs': [], 'learning_rate': [], 'domain_complexity': [],
            'success_probability': []
        }
        
        print("🔬 PROCESSING AI SPECIALIST DATA...")
        
        for i, record in enumerate(training_data):
            if i % 10 == 0 or i == total_records - 1:
                print(f"   Processing record {i+1:,}/{total_records:,}...")
            
            conf_data = record['confidence_assessment']
            train_config = record['training_configuration']
            
            # FIXED: Parse cost from a range string (e.g., "$1,500-4,000")
            cost_str = train_config['automation_cost_monthly']
            numbers = re.findall(r'\d+', cost_str.replace(',', ''))
            cost_monthly = (int(numbers[0]) + int(numbers[1])) / 2 if len(numbers) >= 2 else (int(numbers[0]) if len(numbers) == 1 else 0)

            domain_complexity = (cost_monthly / 1000) * (train_config['epochs'] / 100)
            success_prob = min(1.0, conf_data['target_confidence'] - conf_data['current_confidence'] + np.random.normal(0, 0.1))
                              
            processed_data['confidence_current'].append(conf_data['current_confidence'])
            processed_data['confidence_target'].append(conf_data['target_confidence'])
            processed_data['automation_cost_monthly'].append(cost_monthly)
            processed_data['training_epochs'].append(train_config['epochs'])
            processed_data['learning_rate'].append(train_config['learning_rate'])
            processed_data['domain_complexity'].append(domain_complexity)
            processed_data['success_probability'].append(success_prob)
        
        df = pd.DataFrame(processed_data)
        print(f"\n🎯 DATA PROCESSED: Final Records: {len(df):,}")
        return df
        
    except FileNotFoundError:
        print(f"❌ {filepath.upper()} NOT FOUND!")
        return pd.DataFrame()
    except KeyError as e:
        print(f"💥 KeyError: The key {e} was not found. The file structure might be different than expected.")
        print("   This version expects data in: data['examples']['internal_training']")
        return pd.DataFrame()
    except Exception as e:
        print(f"💥 AN UNEXPECTED ERROR OCCURRED: {e}")
        raise

def ultimate_showdown(data_file='beast_food.json'):
    """The ultimate showdown: LayerMatrix vs the dataset!"""
    print("🚀" * 30)
    print(f"🚀 THE ULTIMATE SHOWDOWN: LAYERMATRIX vs {data_file.upper()}! 🚀")
    print("🚀" * 30)
    
    df = process_training_data(filepath=data_file)
    if df.empty:
        print("Data processing failed. Aborting showdown.")
        return None, 0
    
    feature_columns = ['confidence_current', 'confidence_target', 'automation_cost_monthly',
                      'training_epochs', 'learning_rate', 'domain_complexity']
    X = df[feature_columns].values
    y = (df['success_probability'] > 0.5).astype(int).values
    
    print(f"\n🎯 DATASET PREPARED: {X.shape[0]:,} samples, {np.sum(y):,} positive targets ({np.mean(y):.1%})") # type: ignore
    
    X_scaled = StandardScaler().fit_transform(X)
    X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42, stratify=y) # type: ignore
    print(f"📊 TRAIN-TEST SPLIT: Training: {X_train.shape[0]:,} samples, Testing: {X_test.shape[0]:,} samples")
    
    # Alias UltimateLayerMatrix to LayerMatrix for compatibility
    UltimateLayerMatrix = LayerMatrix
    model = UltimateLayerMatrix(hidden_layers=[128, 64, 32], batch_size=512)
    model.fit(X_train, y_train, validation_split=0.2, epochs=30, verbose=True)
    
    print(f"\n⚔️ TESTING THE CHAMPION...")
    test_score = model.score(X_test, y_test)
    
    print(f"\n🏆 ULTIMATE LAYERMATRIX RESULTS:")
    print("=" * 60)
    print(f"   Test Accuracy: {test_score:.3f}")
    
    print(f"\n📋 DETAILED CLASSIFICATION REPORT:")
    print(classification_report(y_test, model.predict(X_test), zero_division=0))
    
    model.plot_training_history()
    
    print(f"\n🎬 The LayerMatrix revolution scales to production!")
    print("=" * 60)
    return model, test_score

def inspect_full_dataset(filepath='beast_food.json'):
    """
    Loads the full dataset and provides a summary of all available data categories.
    """
    print(f"🔬 Conducting a full inspection of {filepath.upper()}...")
    try:
        with open(filepath, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"❌ ERROR: File not found at {filepath}")
        return
    except json.JSONDecodeError:
        print(f"❌ ERROR: Could not decode JSON from {filepath}")
        return

    if 'examples' not in data:
        print("❌ ERROR: Could not find the 'examples' key in the JSON structure.")
        return

    print("\n" + "="*60)
    print("      FULL DATASET INSPECTION REPORT")
    print("="*60)

    example_sets = data['examples']
    
    summary = {}
    for category, records in example_sets.items():
        summary[category] = len(records)

    print("\n📊 DATASET SUMMARY:")
    for category, count in summary.items():
        print(f"   - Category '{category}': {count:,} records")

    for category, records in example_sets.items():
        print("\n" + "="*60)
        print(f"📁 Sample from Category: '{category}'")
        print("="*60)
        
        if records:
            # Pretty print the first record to show its structure
            sample_record = records[0]
            print(json.dumps(sample_record, indent=4))
        else:
            print("   - No records found in this category.")
    
    print("\n" + "="*60)
    print("INSPECTION COMPLETE")
    print("="*60)

def create_training_prompts(df):
    """
    Takes the processed emotion DataFrame and adds a new column
    containing formatted prompts for fine-tuning a language model.
    """
    print("\n⚙️  Generating final training prompts...")
    
    prompts = []
    for index, row in df.iterrows():
        emotion = row['emotion']
        standard_text = row['standard_text']
        
        # This structure clearly tells the AI its task, input, and where to put the output.
        prompt = (
            f"Adapt the following text to sound more '{emotion}'.\n\n"
            f"### Standard Text:\n{standard_text}\n\n"
            f"### Adapted Text:"
        )
        prompts.append(prompt)
    
    # Add the new column to the DataFrame
    df['training_prompt'] = prompts
    
    print("✅ Final training dataset is ready!")
    print("   This data can now be used to fine-tune a powerful language model.")
    return df


def create_knowledge_corpus(filepath='beast_food.json', output_corpus_file='arkona_knowledge_corpus.txt'):
    """
    Iterates through all datasets, extracts meaningful text fields,
    and compiles them into a single large text file, with one piece of text per line.
    """
    print(f"\n🛠️  Building Knowledge Corpus from {filepath.upper()}...")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        all_text_entries = []
        
        # 1. Process 'emotion_context'
        for record in data.get('examples', {}).get('emotion_context', []):
            all_text_entries.append(record.get('context_input', {}).get('query', ''))
            all_text_entries.append(record.get('standard_response', {}).get('content', ''))
            all_text_entries.append(record.get('emotion_adapted_response', {}).get('content', ''))

        # 2. Process 'ethical_reasoning'
        for record in data.get('examples', {}).get('ethical_reasoning', []):
            all_text_entries.append(record.get('scenario', ''))
            all_text_entries.append(record.get('decision', ''))
            all_text_entries.append(record.get('rationale', ''))
        
        # 3. Process 'curiosity_driven'
        for record in data.get('examples', {}).get('curiosity_driven', []):
            all_text_entries.append(record.get('context', ''))
            all_text_entries.append(record.get('investigation', {}).get('strategy', ''))
            all_text_entries.append(record.get('learning', {}).get('new_knowledge', ''))

        # Filter out any empty strings that may have been added and strip whitespace
        all_text_entries = [text.strip() for text in all_text_entries if isinstance(text, str) and text.strip()]
        
        print(f"   - Extracted a total of {len(all_text_entries):,} unique text entries.")

        # Write all extracted text to the corpus file
        with open(output_corpus_file, 'w', encoding='utf-8') as f:
            for text in all_text_entries:
                f.write(text + '\n')
        
        print(f"✅ Knowledge Corpus successfully created and saved to '{output_corpus_file}'")

    except Exception as e:
        print(f"❌ ERROR creating knowledge corpus: {e}")

def discover_data_format(model_file):
    """
    Inspects a JSON model/data file and prints/discovers its data format.
    Returns a summary of top-level keys and a sample of each major section.
    """
    print(f"\n🔎 Discovering data format for: {model_file}")
    try:
        with open(model_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"❌ ERROR: File not found at {model_file}")
        return None
    except json.JSONDecodeError:
        print(f"❌ ERROR: Could not decode JSON from {model_file}")
        return None

    def print_sample(obj, indent=0, max_items=3):
        pad = '  ' * indent
        if isinstance(obj, dict):
            print(f"{pad}{{...}} (dict with {len(obj)} keys)")
            for k, v in list(obj.items())[:max_items]:
                print(f"{pad}  Key: '{k}' → Type: {type(v).__name__}")
                print_sample(v, indent+2, max_items)
        elif isinstance(obj, list):
            print(f"{pad}[...] (list with {len(obj)} items)")
            for i, v in enumerate(obj[:max_items]):
                print(f"{pad}  Item {i} → Type: {type(v).__name__}")
                print_sample(v, indent+2, max_items)
        else:
            print(f"{pad}{repr(obj)[:80]}")

    print("\nTop-level keys:", list(data.keys()))
    for k, v in data.items():
        print(f"\nKey: '{k}' → Type: {type(v).__name__}")
        print_sample(v, indent=1)
    print("\n✅ Data format discovery complete.")
    return data


