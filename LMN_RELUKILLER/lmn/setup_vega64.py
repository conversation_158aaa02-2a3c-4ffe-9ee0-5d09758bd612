#!/usr/bin/env python3
"""
Setup script for Vega 64 GPU acceleration with MassiveLayerMatrix.
This script helps install and configure OpenCL for optimal performance.
"""

import subprocess
import sys
import os

def check_opencl_support():
    """Check if OpenCL is available on the system."""
    try:
        import pyopencl as cl
        platforms = cl.get_platforms()
        
        print("🔍 OpenCL Platforms detected:")
        for i, platform in enumerate(platforms):
            print(f"   {i}: {platform.name} ({platform.vendor})")
            
            try:
                devices = platform.get_devices()
                for j, device in enumerate(devices):
                    print(f"      Device {j}: {device.name}")
                    print(f"         Type: {cl.device_type.to_string(device.type)}")
                    print(f"         Memory: {device.global_mem_size // (1024**3)}GB")
                    print(f"         Compute Units: {device.max_compute_units}")
            except:
                print("      No devices found")
        
        return True
        
    except ImportError:
        print("❌ PyOpenCL not installed")
        return False
    except Exception as e:
        print(f"❌ OpenCL error: {e}")
        return False

def install_pyopencl():
    """Install PyOpenCL for Vega 64 support."""
    print("📦 Installing PyOpenCL for Vega 64...")
    
    try:
        # Try to install PyOpenCL
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyopencl"])
        print("✅ PyOpenCL installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install PyOpenCL")
        print("   You may need to install OpenCL drivers manually")
        print("   For macOS with Vega 64: OpenCL should be available by default")
        return False

def test_vega64_performance():
    """Test Vega 64 performance with MassiveLayerMatrix."""
    print("\n🧪 Testing Vega 64 performance...")
    
    try:
        import numpy as np
        import time
        
        # Import our module
        import importlib.util
        spec = importlib.util.spec_from_file_location("MassiveLayerMatrix", "lmn/MassiveLayerMatrix.py")
        massive_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(massive_module)
        MassiveLayerMatrix = massive_module.MassiveLayerMatrix
        
        # Create test data
        print("   Creating test dataset...")
        np.random.seed(42)
        X = np.random.randn(1000, 256).astype(np.float32)
        y = (np.sum(X[:, :10], axis=1) > 0).astype(int)
        
        # Test GPU-optimized model
        print("   Testing GPU-optimized model...")
        start_time = time.time()
        
        model = MassiveLayerMatrix(
            hidden_layers=[512, 1024, 512],
            micro_batch_size=16,
            gradient_accumulation_steps=16,
            max_memory_gb=8.0,
            use_mixed_precision=True,
            checkpoint_layers=True,
            offload_to_cpu=False
        )
        
        model.fit(X, y, epochs=3, verbose=False)
        predictions = model.predict(X[-200:])
        accuracy = np.mean(predictions == y[-200:])
        
        total_time = time.time() - start_time
        
        print(f"   ✅ Performance test completed!")
        print(f"      Model parameters: {model._estimate_parameters():,}")
        print(f"      Training time: {total_time:.2f}s")
        print(f"      Test accuracy: {accuracy:.3f}")
        print(f"      Throughput: {model._estimate_parameters() / total_time / 1e6:.1f}M params/sec")
        
        model.cleanup()
        return True
        
    except Exception as e:
        print(f"   ❌ Performance test failed: {e}")
        return False

def optimize_for_vega64():
    """Provide optimization tips for Vega 64."""
    print("\n🚀 VEGA 64 OPTIMIZATION TIPS:")
    print("=" * 50)
    print("1. Memory Configuration:")
    print("   - Vega 64 has 8GB HBM2 memory")
    print("   - Set max_memory_gb=8.0 for full utilization")
    print("   - Use mixed precision (16-bit) to double effective memory")
    print()
    print("2. Compute Configuration:")
    print("   - Vega 64 has 4096 stream processors")
    print("   - Use larger hidden layers (512, 1024, 2048)")
    print("   - Batch sizes of 16-64 work well")
    print()
    print("3. LayerMatrix Advantages on Vega 64:")
    print("   - Information accumulation reduces memory bandwidth")
    print("   - Tanh activations are efficient on AMD architecture")
    print("   - Global context computation parallelizes well")
    print()
    print("4. Recommended Model Sizes:")
    print("   - Small: [512, 256] (~500K params)")
    print("   - Medium: [1024, 512] (~2M params)")
    print("   - Large: [2048, 1024] (~8M params)")
    print("   - Massive: [4096, 2048] (~32M params)")
    print("   - Ultra: [8192, 4096] (~128M params)")
    print()
    print("5. For 28B parameter models:")
    print("   - Use gradient_accumulation_steps=1024+")
    print("   - Enable checkpoint_layers=True")
    print("   - Set micro_batch_size=1-4")
    print("   - Use offload_to_cpu=True for largest models")

def main():
    """Main setup function."""
    print("🚀 VEGA 64 SETUP FOR MASSIVE LAYERMATRIX")
    print("=" * 60)
    print("Setting up GPU acceleration for 28B+ parameter training")
    print("on AMD Vega 64 with 8GB HBM2 memory.")
    print()
    
    # Check current OpenCL status
    opencl_available = check_opencl_support()
    
    if not opencl_available:
        print("\n📦 Installing OpenCL support...")
        if install_pyopencl():
            opencl_available = check_opencl_support()
    
    if opencl_available:
        print("\n✅ OpenCL is available!")
        
        # Test performance
        if test_vega64_performance():
            print("\n🏆 Vega 64 is ready for massive model training!")
        else:
            print("\n⚠️  Performance test failed, but basic functionality works")
    else:
        print("\n⚠️  OpenCL not available, using CPU fallback")
        print("   MassiveLayerMatrix will still work but without GPU acceleration")
    
    # Provide optimization tips
    optimize_for_vega64()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Run: python3 quick_test.py")
    print("2. Run: python3 demo_28b_training.py")
    print("3. Monitor GPU usage with: sudo powermetrics --samplers gpu_power -n 1")
    print("4. For maximum performance, close other GPU-intensive applications")
    
    print(f"\n✅ Setup complete! Ready to train massive models on Vega 64!")

if __name__ == "__main__":
    main()
