
import numpy as np

class LayerMatrixSeq2SeqEncoder:
    def __init__(self, input_dim, hidden_dims):
        self.hidden_dims = hidden_dims
        self.params = []
        layer_sizes = [input_dim] + hidden_dims

        for i in range(len(layer_sizes) - 1):
            w = np.random.randn(layer_sizes[i], layer_sizes[i+1]) * np.sqrt(2.0 / layer_sizes[i])
            b = np.zeros(layer_sizes[i+1])
            self.params.append((w, b))

    def forward(self, input_seq):
        """
        input_seq: shape (T, input_dim) -- sequence of T time steps
        Returns final hidden state and all intermediate states
        """
        h = input_seq
        states = []

        for (w, b) in self.params:
            h = np.tanh(np.dot(h, w) + b)  # apply tanh activation per layer
            states.append(h)

        return h, states
