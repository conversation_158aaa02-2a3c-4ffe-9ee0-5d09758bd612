import numpy as np

class Seq2SeqTrainer:
    def __init__(self, encoder, decoder, lr=0.01, batch_size=16):
        self.encoder = encoder
        self.decoder = decoder
        self.lr = lr
        self.batch_size = batch_size

    def cross_entropy(self, logits, target_ids):
        probs = self._softmax(logits)
        correct = probs[np.arange(len(target_ids)), target_ids]
        return -np.mean(np.log(correct + 1e-9))

    def _softmax(self, x):
        e_x = np.exp(x - np.max(x, axis=1, keepdims=True))
        return e_x / np.sum(e_x, axis=1, keepdims=True)

    def train_step(self, input_seq_batch, target_seq_batch):
        losses = []
        for input_seq, target_seq in zip(input_seq_batch, target_seq_batch):
            enc_out, _ = self.encoder.forward(input_seq)
            logits = self.decoder.forward(target_seq[:-1], encoder_context=enc_out)
            loss = self.cross_entropy(logits, target_seq[1:])
            losses.append(loss)
            # Here you'd accumulate gradients if doing true backprop

        return np.mean(losses)
