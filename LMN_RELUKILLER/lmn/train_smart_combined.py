#!/usr/bin/env python3
"""
Smart Combined Training for LayerMatrix

Combines the best datasets without creating a monster model! 😅
Quick training: ~5-10 minutes on Vega 64
Total: ~1000 samples = manageable model size
"""

import sys
import os
import numpy as np
import json
import time
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

# Import our modules
sys.path.insert(0, '.')

import importlib.util
spec = importlib.util.spec_from_file_location("MassiveLayerMatrix", "lmn/MassiveLayerMatrix.py")
massive_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(massive_module)
MassiveLayerMatrix = massive_module.MassiveLayerMatrix

class SmartCombinedTrainer:
    """Smart trainer that combines datasets without exploding model size."""
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=1024,  # Smaller feature set for speed
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.9
        )
        
        self.texts = []
        self.labels = []
        self.sources = []
        
        print("🧠 Smart Combined Trainer initialized!")
        print("   Goal: Fast, focused training with diverse data")
        print("   Target: ~1000 samples, <10 minute training")
    
    def load_historical_sample(self, max_samples: int = 600):
        """Load a smart sample from historical datasets."""
        print(f"\n🏛️  Loading historical sample ({max_samples} samples)...")
        
        historical_dir = Path("historical_datasets")
        if not historical_dir.exists():
            print(f"⚠️  Historical directory not found: {historical_dir}")
            return 0
        
        loaded = 0
        
        for hist_file in historical_dir.glob("*.json"):
            if loaded >= max_samples:
                break
                
            try:
                with open(hist_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                training_data = data.get('training_data', [])
                # Take a reasonable sample from each file
                sample_size = min(200, len(training_data), max_samples - loaded)
                
                if training_data and sample_size > 0:
                    # Sample randomly from this file
                    indices = np.random.choice(len(training_data), sample_size, replace=False)
                    
                    for idx in indices:
                        item = training_data[idx]
                        combined_text = f"{item['question']} {item['answer']}"
                        
                        self.texts.append(combined_text)
                        self.labels.append(f"historical_{item.get('category', 'event')}")
                        self.sources.append("historical")
                        loaded += 1
                
                print(f"   📚 {hist_file.name}: {sample_size} samples")
                
            except Exception as e:
                print(f"⚠️  Error loading {hist_file}: {e}")
                continue
        
        print(f"✅ Loaded {loaded} historical samples")
        return loaded
    
    def load_generated_sample(self, max_samples: int = 400):
        """Load a sample from generated Q&A data."""
        print(f"\n🤖 Loading generated sample ({max_samples} samples)...")
        
        # Look for generated files in the generator directory
        gen_dirs = [
            Path("/Users/<USER>/Desktop/arkona_train/generator/generate"),
            Path("/Users/<USER>/Desktop/arkona_train/generator/generate_v2"),
            Path(".")
        ]
        
        loaded = 0
        
        for gen_dir in gen_dirs:
            if loaded >= max_samples:
                break
                
            generated_files = list(gen_dir.glob("*qa*.json"))
            
            for gen_file in generated_files:
                if loaded >= max_samples:
                    break
                    
                try:
                    with open(gen_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Handle different formats
                    qa_pairs = []
                    if 'training_data' in data:
                        qa_pairs = data['training_data']
                    elif 'qa_pairs' in data:
                        qa_pairs = data['qa_pairs']
                    
                    sample_size = min(100, len(qa_pairs), max_samples - loaded)
                    
                    if qa_pairs and sample_size > 0:
                        indices = np.random.choice(len(qa_pairs), sample_size, replace=False)
                        
                        for idx in indices:
                            item = qa_pairs[idx]
                            if 'input_text' in item and 'target_text' in item:
                                combined_text = f"{item['input_text']} {item['target_text']}"
                            else:
                                combined_text = f"{item.get('question', '')} {item.get('answer', '')}"
                            
                            self.texts.append(combined_text)
                            self.labels.append(f"generated_{item.get('domain', 'qa')}")
                            self.sources.append("generated")
                            loaded += 1
                    
                    print(f"   🤖 {gen_file.name}: {sample_size} samples")
                    
                except Exception as e:
                    print(f"⚠️  Error loading {gen_file}: {e}")
                    continue
        
        print(f"✅ Loaded {loaded} generated samples")
        return loaded
    
    def prepare_training_data(self):
        """Prepare the combined data for training."""
        print(f"\n🔄 Preparing training data...")
        print(f"   Total samples: {len(self.texts)}")
        
        if len(self.texts) == 0:
            print("❌ No data loaded!")
            return None, None
        
        # Show source distribution
        source_counts = {}
        for source in self.sources:
            source_counts[source] = source_counts.get(source, 0) + 1
        print(f"   Sources: {source_counts}")
        
        # Vectorize text
        print("🔤 Vectorizing text...")
        X = self.vectorizer.fit_transform(self.texts).toarray().astype(np.float32)
        
        # Encode labels
        label_encoder = LabelEncoder()
        y = label_encoder.fit_transform(self.labels)
        
        print(f"✅ Data prepared:")
        print(f"   Features: {X.shape[1]}")
        print(f"   Classes: {len(label_encoder.classes_)}")
        
        return X, y
    
    def train_smart_model(self, X, y):
        """Train a smart, manageable LayerMatrix model."""
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"\n🧠 Creating smart LayerMatrix model...")
        print(f"   Training samples: {len(X_train):,}")
        print(f"   Test samples: {len(X_test):,}")
        print(f"   Features: {X.shape[1]:,}")
        print(f"   Classes: {len(np.unique(y))}")
        
        # Create smart model - optimized for speed
        model = MassiveLayerMatrix(
            hidden_layers=[512, 256],     # Manageable size
            accumulation_ratio=0.15,
            learning_rate=5e-4,           # Higher for faster convergence
            micro_batch_size=16,
            gradient_accumulation_steps=8,
            max_memory_gb=8.0,
            use_mixed_precision=True,
            adaptive_accumulation=True,
            layer_norm=True,
            dropout_rate=0.15,
            weight_decay=1e-4,
            warmup_steps=50
        )
        
        print(f"🎯 Model created: {model._estimate_parameters():,} parameters")
        
        # Train efficiently
        print(f"\n🚀 Training smart model (target: <10 minutes)...")
        start_time = time.time()
        
        # Quick training - fewer epochs for speed
        model.fit(X_train_scaled, y_train, epochs=6, verbose=True)
        
        train_time = time.time() - start_time
        
        # Evaluate
        print(f"\n📊 Evaluating model...")
        predictions = model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, predictions)
        
        print(f"\n🏆 SMART TRAINING COMPLETE!")
        print(f"   Accuracy: {accuracy:.3f}")
        print(f"   Training time: {train_time/60:.1f} minutes")
        print(f"   Parameters: {model._estimate_parameters():,}")
        print(f"   Efficiency: {accuracy/(train_time/60):.2f} accuracy/minute")
        
        model.cleanup()
        return accuracy, train_time
    
    def run_smart_training(self):
        """Run the complete smart training pipeline."""
        print("🚀 SMART COMBINED LAYERMATRIX TRAINING")
        print("=" * 60)
        print("Goal: Fast, diverse training without model explosion! 😅")
        
        # Load smart samples from available sources
        historical_count = self.load_historical_sample(max_samples=600)
        generated_count = self.load_generated_sample(max_samples=400)
        
        total_samples = historical_count + generated_count
        
        if total_samples == 0:
            print("❌ No data loaded. Check data sources.")
            return 0.0
        
        print(f"\n📊 SMART DATASET SUMMARY:")
        print(f"   Historical: {historical_count} samples") 
        print(f"   Generated: {generated_count} samples")
        print(f"   Total: {total_samples} samples")
        print(f"   Target: Manageable model, fast training! 🎯")
        
        # Prepare and train
        X, y = self.prepare_training_data()
        
        if X is not None:
            accuracy, train_time = self.train_smart_model(X, y)
            
            print(f"\n🎉 SUCCESS!")
            print(f"   ✅ Trained on diverse, real-world data")
            print(f"   ✅ Achieved {accuracy:.1%} accuracy")
            print(f"   ✅ Completed in {train_time/60:.1f} minutes")
            print(f"   ✅ Ready for production use!")
            
            return accuracy
        
        return 0.0


def main():
    """Main training function."""
    trainer = SmartCombinedTrainer()
    accuracy = trainer.run_smart_training()
    
    if accuracy > 0.7:
        print(f"\n🏆 EXCELLENT RESULTS! ({accuracy:.1%})")
    elif accuracy > 0.5:
        print(f"\n👍 GOOD RESULTS! ({accuracy:.1%})")
    else:
        print(f"\n🔧 NEEDS TUNING ({accuracy:.1%})")
    
    print(f"\n🚀 Smart training complete - ready for real-world deployment!")


if __name__ == "__main__":
    main()
