#!/usr/bin/env python3
"""
Historical Data Processor for LayerMatrix Training

Smart processor for the 10,000 years of human history dataset.
Creates manageable, focused datasets to avoid 1T parameter models! 😅

Features:
- Small dataset creation (1K-10K samples)
- Time period filtering (e.g., just 20th century)
- Category filtering (wars, discoveries, politics, etc.)
- Confidence-based filtering (high-quality events only)
- Perfect for LayerMatrix training without exploding model size
"""

import json
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
import time
from collections import Counter

class HistoricalDataProcessor:
    """Process massive historical dataset into manageable LayerMatrix training sets."""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.events = []
        self.loaded = False
        
        print(f"🏛️  Historical Data Processor initialized")
        print(f"   Dataset: {self.dataset_path}")
    
    def load_sample_events(self, max_events: int = 1000):
        """Load a sample of events to avoid memory explosion."""
        print(f"📚 Loading sample of {max_events:,} events...")

        try:
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                # Try loading as complete JSON array first
                try:
                    data = json.load(f)
                    if isinstance(data, list):
                        # Filter out non-dict entries and take sample
                        valid_events = [event for event in data if isinstance(event, dict)]
                        self.events = valid_events[:max_events]
                        self.loaded = True
                        print(f"✅ Loaded {len(self.events):,} historical events")
                        return len(self.events)
                except json.JSONDecodeError:
                    # If that fails, try line-by-line
                    f.seek(0)
                    pass

                # Line-by-line parsing fallback
                events_loaded = 0
                for line_num, line in enumerate(f):
                    if events_loaded >= max_events:
                        break

                    line = line.strip()
                    if not line or line in [',', '[', ']']:
                        continue

                    # Clean up line (remove trailing comma)
                    if line.endswith(','):
                        line = line[:-1]

                    try:
                        event = json.loads(line)
                        if isinstance(event, dict):
                            self.events.append(event)
                            events_loaded += 1
                        elif isinstance(event, list):
                            # Handle nested arrays
                            for item in event:
                                if isinstance(item, dict) and events_loaded < max_events:
                                    self.events.append(item)
                                    events_loaded += 1
                    except json.JSONDecodeError as e:
                        if line_num < 10:  # Only show first few errors
                            print(f"⚠️  Skipping malformed line {line_num}: {str(e)[:50]}...")
                        continue

            self.loaded = True
            print(f"✅ Loaded {len(self.events):,} historical events")

            return len(self.events)

        except Exception as e:
            print(f"❌ Error loading dataset: {e}")
            return 0
    
    def analyze_dataset(self):
        """Analyze the loaded events to understand the data structure."""
        if not self.loaded or not self.events:
            print("⚠️  No events loaded. Call load_sample_events() first.")
            return
        
        print(f"\n📊 DATASET ANALYSIS")
        print("=" * 50)
        
        # Basic stats
        print(f"Total events: {len(self.events):,}")
        
        # Categories
        categories = [event.get('category', 'unknown') for event in self.events]
        category_counts = Counter(categories)
        print(f"\n📂 Categories:")
        for cat, count in category_counts.most_common(10):
            print(f"   {cat}: {count:,}")
        
        # Time periods
        years = []
        for event in self.events:
            try:
                earliest = event.get('earliest_date_year')
                if earliest and isinstance(earliest, (int, float)) and earliest > 0:
                    years.append(int(earliest))
            except:
                continue
        
        if years:
            print(f"\n📅 Time Range:")
            print(f"   Earliest: {min(years)} CE")
            print(f"   Latest: {max(years)} CE")
            print(f"   Span: {max(years) - min(years):,} years")
        
        # Confidence scores
        confidences = [event.get('confidence', 0) for event in self.events if event.get('confidence')]
        if confidences:
            print(f"\n🎯 Confidence Scores:")
            print(f"   Mean: {np.mean(confidences):.3f}")
            print(f"   High confidence (>0.7): {sum(1 for c in confidences if c > 0.7):,}")
        
        # Sample event
        if self.events:
            sample = self.events[0]
            print(f"\n📝 Sample Event:")
            print(f"   Title: {sample.get('title', 'N/A')[:100]}...")
            print(f"   Category: {sample.get('category', 'N/A')}")
            print(f"   Year: {sample.get('earliest_date_year', 'N/A')}")
            print(f"   Confidence: {sample.get('confidence', 'N/A')}")
    
    def create_focused_dataset(self, 
                             category_filter: Optional[str] = None,
                             year_range: Optional[tuple] = None,
                             min_confidence: float = 0.3,
                             max_samples: int = 1000) -> List[Dict]:
        """Create a focused dataset for LayerMatrix training."""
        
        if not self.loaded:
            print("⚠️  Load events first with load_sample_events()")
            return []
        
        print(f"\n🎯 Creating focused dataset...")
        print(f"   Category filter: {category_filter or 'All'}")
        print(f"   Year range: {year_range or 'All'}")
        print(f"   Min confidence: {min_confidence}")
        print(f"   Max samples: {max_samples:,}")
        
        filtered_events = []
        
        for event in self.events:
            # Category filter
            if category_filter and event.get('category') != category_filter:
                continue
            
            # Year range filter
            if year_range:
                year = event.get('earliest_date_year')
                if not year or year < year_range[0] or year > year_range[1]:
                    continue
            
            # Confidence filter
            confidence = event.get('confidence', 0)
            if confidence < min_confidence:
                continue
            
            # Check for meaningful content
            title = event.get('title', '')
            source_text = event.get('source_text', '')
            if len(title) < 20 and len(source_text) < 50:
                continue
            
            filtered_events.append(event)
            
            if len(filtered_events) >= max_samples:
                break
        
        print(f"✅ Filtered to {len(filtered_events):,} high-quality events")
        return filtered_events
    
    def convert_to_qa_format(self, events: List[Dict]) -> List[Dict]:
        """Convert historical events to Q&A format for LayerMatrix training."""
        
        qa_pairs = []
        
        for i, event in enumerate(events):
            try:
                # Extract key information
                title = event.get('title', '').strip()
                source_text = event.get('source_text', '').strip()
                category = event.get('category', 'historical_event')
                year = event.get('earliest_date_year')
                summary = event.get('summary', '').strip()
                
                # Create meaningful content
                if summary and len(summary) > 50:
                    content = summary
                elif len(source_text) > 100:
                    content = source_text[:500] + "..." if len(source_text) > 500 else source_text
                else:
                    content = title
                
                if len(content) < 30:
                    continue
                
                # Generate Q&A pairs
                qa_templates = [
                    {
                        "question": f"What happened in {int(year) if year else 'this historical period'}?",
                        "answer": content
                    },
                    {
                        "question": f"Describe the {category.replace('_', ' ')} event: {title[:50]}...",
                        "answer": content
                    }
                ]
                
                if year and year > 0:
                    qa_templates.append({
                        "question": f"What significant event occurred around {int(year)}?",
                        "answer": f"In {int(year)}, {content}"
                    })
                
                for template in qa_templates:
                    qa_pair = {
                        "id": f"hist-{i}-{len(qa_pairs)}",
                        "question": template["question"],
                        "answer": template["answer"],
                        "domain": "historical_events",
                        "category": category,
                        "year": int(year) if year else None,
                        "confidence": event.get('confidence', 0),
                        "metadata": {
                            "source": "historical_dataset",
                            "event_id": event.get('event_id'),
                            "original_title": title
                        }
                    }
                    qa_pairs.append(qa_pair)
            
            except Exception as e:
                print(f"⚠️  Error processing event {i}: {e}")
                continue
        
        print(f"✅ Created {len(qa_pairs):,} Q&A pairs from historical events")
        return qa_pairs
    
    def create_small_training_datasets(self):
        """Create several small, focused training datasets."""
        
        datasets = {}
        
        # Dataset 1: Modern History (20th-21st century)
        print(f"\n📚 Creating Modern History dataset...")
        modern_events = self.create_focused_dataset(
            year_range=(1900, 2025),
            min_confidence=0.4,
            max_samples=500
        )
        if modern_events:
            datasets['modern_history'] = self.convert_to_qa_format(modern_events)
        
        # Dataset 2: Wars and Conflicts
        print(f"\n⚔️  Creating Wars & Conflicts dataset...")
        war_events = self.create_focused_dataset(
            category_filter='war_conflict',
            min_confidence=0.3,
            max_samples=300
        )
        if war_events:
            datasets['wars_conflicts'] = self.convert_to_qa_format(war_events)
        
        # Dataset 3: High Confidence Events (any period)
        print(f"\n🎯 Creating High Confidence dataset...")
        high_conf_events = self.create_focused_dataset(
            min_confidence=0.7,
            max_samples=400
        )
        if high_conf_events:
            datasets['high_confidence'] = self.convert_to_qa_format(high_conf_events)
        
        return datasets
    
    def save_datasets(self, datasets: Dict[str, List[Dict]], output_dir: str = "historical_datasets"):
        """Save the created datasets for LayerMatrix training."""
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        total_samples = 0
        
        for name, qa_pairs in datasets.items():
            if not qa_pairs:
                continue
            
            # Create LayerMatrix format
            dataset = {
                "metadata": {
                    "dataset_name": name,
                    "source": "10k_years_human_history",
                    "created_at": time.time(),
                    "total_samples": len(qa_pairs),
                    "format": "layermatrix_historical_training"
                },
                "training_data": qa_pairs
            }
            
            # Save dataset
            output_file = output_path / f"historical_{name}_{len(qa_pairs)}_samples.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(dataset, f, indent=2, ensure_ascii=False)
            
            total_samples += len(qa_pairs)
            print(f"💾 Saved {name}: {len(qa_pairs):,} samples → {output_file}")
        
        print(f"\n🎉 DATASETS CREATED!")
        print(f"   Total samples: {total_samples:,}")
        print(f"   Output directory: {output_path}")
        print(f"   Ready for LayerMatrix training! 🚀")
        
        return output_path


def main():
    """Main processing function."""
    print("🏛️  HISTORICAL DATA PROCESSOR FOR LAYERMATRIX")
    print("=" * 70)
    print("Creating small, focused datasets from 10,000 years of history")
    print("(Avoiding 1T parameter models! 😅)")
    
    # Initialize processor
    dataset_path = "/Users/<USER>/Desktop/arkona_train/datasets/final_aggregated_quantum_20250625_211650.json"
    processor = HistoricalDataProcessor(dataset_path)
    
    # Load sample events (start small!)
    events_loaded = processor.load_sample_events(max_events=2000)  # Small sample
    
    if events_loaded == 0:
        print("❌ No events loaded. Check dataset path.")
        return
    
    # Analyze the data
    processor.analyze_dataset()
    
    # Create focused datasets
    datasets = processor.create_small_training_datasets()
    
    # Save for LayerMatrix training
    if datasets:
        output_dir = processor.save_datasets(datasets)
        
        print(f"\n🚀 READY FOR LAYERMATRIX TRAINING!")
        print(f"   Use these small datasets to avoid parameter explosion")
        print(f"   Each dataset is focused and manageable")
        print(f"   Perfect for testing LayerMatrix on historical knowledge!")
    
    else:
        print("⚠️  No datasets created. Check filters and data quality.")


if __name__ == "__main__":
    main()
