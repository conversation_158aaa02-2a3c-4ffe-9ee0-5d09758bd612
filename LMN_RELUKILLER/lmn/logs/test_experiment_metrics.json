{"training_history": [{"epoch": 0, "batch": 0, "loss": 0.5, "accuracy": 0.6, "gpu_memory": 6.5, "timestamp": 0.0005261898040771484}, {"epoch": 0, "batch": 1, "loss": 0.48, "accuracy": 0.62, "gpu_memory": 6.5, "timestamp": 0.0005609989166259766}, {"epoch": 0, "batch": 2, "loss": 0.46, "accuracy": 0.64, "gpu_memory": 6.5, "timestamp": 0.0005891323089599609}, {"epoch": 0, "batch": 3, "loss": 0.44, "accuracy": 0.6599999999999999, "gpu_memory": 6.5, "timestamp": 0.0006158351898193359}, {"epoch": 0, "batch": 4, "loss": 0.42, "accuracy": 0.6799999999999999, "gpu_memory": 6.5, "timestamp": 0.0006430149078369141}, {"epoch": 1, "batch": 0, "loss": 0.4, "accuracy": 0.7, "gpu_memory": 6.5, "timestamp": 0.0007879734039306641}, {"epoch": 1, "batch": 1, "loss": 0.38, "accuracy": 0.72, "gpu_memory": 6.5, "timestamp": 0.0009219646453857422}, {"epoch": 1, "batch": 2, "loss": 0.36, "accuracy": 0.74, "gpu_memory": 6.5, "timestamp": 0.001071929931640625}, {"epoch": 1, "batch": 3, "loss": 0.33999999999999997, "accuracy": 0.76, "gpu_memory": 6.5, "timestamp": 0.0011410713195800781}, {"epoch": 1, "batch": 4, "loss": 0.32, "accuracy": 0.78, "gpu_memory": 6.5, "timestamp": 0.0011849403381347656}, {"epoch": 2, "batch": 0, "loss": 0.3, "accuracy": 0.8, "gpu_memory": 6.5, "timestamp": 0.0014541149139404297}, {"epoch": 2, "batch": 1, "loss": 0.28, "accuracy": 0.82, "gpu_memory": 6.5, "timestamp": 0.0014789104461669922}, {"epoch": 2, "batch": 2, "loss": 0.26, "accuracy": 0.84, "gpu_memory": 6.5, "timestamp": 0.001500844955444336}, {"epoch": 2, "batch": 3, "loss": 0.24, "accuracy": 0.86, "gpu_memory": 6.5, "timestamp": 0.001519918441772461}, {"epoch": 2, "batch": 4, "loss": 0.21999999999999997, "accuracy": 0.88, "gpu_memory": 6.5, "timestamp": 0.0015370845794677734}], "gpu_utilization": [], "memory_usage": [], "accumulation_effectiveness": [], "layer_statistics": [], "accuracy_progression": [{"epoch": 0, "train_loss": 0.42, "train_accuracy": 0.6799999999999999, "val_loss": 0.378, "val_accuracy": 0.714, "learning_rate": 0.0001, "epoch_time": 0.0006449222564697266, "best_accuracy": 0.714}, {"epoch": 1, "train_loss": 0.32, "train_accuracy": 0.78, "val_loss": 0.28800000000000003, "val_accuracy": 0.8190000000000001, "learning_rate": 0.0001, "epoch_time": 0.001188039779663086, "best_accuracy": 0.8190000000000001}, {"epoch": 2, "train_loss": 0.21999999999999997, "train_accuracy": 0.88, "val_loss": 0.19799999999999998, "val_accuracy": 0.924, "learning_rate": 0.0001, "epoch_time": 0.0015380382537841797, "best_accuracy": 0.924}], "timing_data": {}, "model_config": {"hidden_layers": [1024, 512, 256], "accumulation_ratio": 0.15, "learning_rate": 0.0001, "batch_size": 16}, "dataset_info": {"samples": 9456, "features": 2048, "classes": 24, "source": "Arkona real-world data"}, "training_start_time": "2025-07-15T17:43:25.283609", "training_end_time": "2025-07-15T17:43:25.285307", "total_training_time": 120.5, "final_accuracy": 0.85, "best_accuracy": 0.924, "final_metrics": {"parameters": 1968896, "throughput": "16K samples/s"}}