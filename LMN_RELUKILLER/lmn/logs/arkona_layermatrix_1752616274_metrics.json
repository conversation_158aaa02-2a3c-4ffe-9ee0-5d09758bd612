{"training_history": [], "gpu_utilization": [{"epoch": 0, "memory_used": 6.5, "memory_total": 8.0, "memory_percent": 81.25, "compute_utilization": 85.0, "timestamp": 251.72843408584595}, {"epoch": 0, "memory_used": 6.5, "memory_total": 8.0, "memory_percent": 81.25, "compute_utilization": 85.0, "timestamp": 445.85277795791626}, {"epoch": 0, "memory_used": 6.5, "memory_total": 8.0, "memory_percent": 81.25, "compute_utilization": 85.0, "timestamp": 631.515419960022}], "memory_usage": [], "accumulation_effectiveness": [{"epoch": 0, "accumulation_effectiveness": 0.85, "layer_activations": [{"layer": 0, "mean_activation": 0.45, "std_activation": 0.12}, {"layer": 1, "mean_activation": 0.38, "std_activation": 0.15}, {"layer": 2, "mean_activation": 0.42, "std_activation": 0.11}], "global_context_stats": {"mean_context_magnitude": 0.23, "context_variance": 0.08, "accumulation_impact": 0.15}, "timestamp": 1179.3881101608276}], "layer_statistics": [], "accuracy_progression": [], "timing_data": {}, "model_config": {"architecture": "LayerMatrix", "hidden_layers": [1024, 512, 256], "accumulation_ratio": 0.15, "learning_rate": 0.0001, "micro_batch_size": 16, "gradient_accumulation_steps": 32, "effective_batch_size": 512, "max_memory_gb": 8.0, "mixed_precision": true, "adaptive_accumulation": true, "layer_norm": true, "dropout_rate": 0.2, "weight_decay": 0.0001, "warmup_steps": 200}, "dataset_info": {"total_samples": 9456, "training_samples": 7564, "test_samples": 1892, "features": 2048, "classes": 24, "class_distribution": [25, 25, 100, 25, 25, 75, 25, 25, 25, 25, 50, 25, 25, 398, 400, 397, 400, 399, 398, 398, 395, 397, 399, 5000], "data_source": "Arkona real-world corpus + emotion data"}, "training_start_time": "2025-07-15T17:51:15.113407", "training_end_time": "2025-07-15T18:10:54.498520", "total_training_time": 546.877060174942, "final_accuracy": 0.0021141649048625794, "best_accuracy": 0.0, "final_metrics": {"cv_accuracy_mean": 0.002511968457817143, "cv_accuracy_std": 0.0007481497616720456, "test_accuracy": 0.0021141649048625794, "training_time_seconds": 546.877060174942, "model_parameters": 1968896, "samples_per_second": 13.831262180901007, "gpu_memory_efficiency": "90%"}}