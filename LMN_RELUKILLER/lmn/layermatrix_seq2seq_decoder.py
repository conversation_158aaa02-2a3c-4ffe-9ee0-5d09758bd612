import numpy as np
from matrix_self_attention import MatrixSelfAttention  # Ensure this is in your path

class LayerMatrixSeq2SeqDecoder:
    def __init__(self, vocab_size=10000, d_model=256, d_ff=1024, n_layers=4, n_heads=8):
        self.vocab_size = vocab_size
        self.d_model = d_model
        self.n_layers = n_layers
        

        self.embedding = np.random.randn(vocab_size, d_model) * 0.01

        # Build decoder layers (attention + FFN)
        self.attn_layers = [MatrixSelfAttention(d_model, n_heads) for _ in range(n_layers)]
        self.ffn_layers = []
        for _ in range(n_layers):
            w1 = np.random.randn(d_model, d_ff) * np.sqrt(2.0 / d_model)
            b1 = np.zeros(d_ff)
            w2 = np.random.randn(d_ff, d_model) * np.sqrt(2.0 / d_ff)
            b2 = np.zeros(d_model)
            self.ffn_layers.append(((w1, b1), (w2, b2)))

        self.out_w = np.random.randn(d_model, vocab_size) * np.sqrt(2.0 / d_model)
        self.out_b = np.zeros(vocab_size)
        self.grad_out_w = np.zeros_like(self.out_w)
        self.grad_out_b = np.zeros_like(self.out_b)
        self.grad_accum = {
            'out_w': np.zeros_like(self.out_w),
            'out_b': np.zeros_like(self.out_b),
            'ffn': [(
                np.zeros_like(w1), np.zeros_like(b1),
                np.zeros_like(w2), np.zeros_like(b2)
            ) for ((w1, b1), (w2, b2)) in self.ffn_layers]
        }


    def forward(self, input_ids, encoder_context=None):
        T = len(input_ids)
        logits = []
        token_reps = []

        for t in range(T):
            token_id = input_ids[t]
            x = self.embedding[token_id].reshape(1, -1)
            token_reps.append(x)

            x_seq = np.vstack(token_reps)  # (t+1, d_model)

            # Apply N attention + FFN layers
            for attn, (dense1, dense2) in zip(self.attn_layers, self.ffn_layers):
                attn_out = attn.forward(x_seq)
                x_seq = x_seq + attn_out  # Residual

                x = x_seq[-1:]  # Get the last output (current token position)

                w1, b1 = dense1
                w2, b2 = dense2
                x = np.tanh(x @ w1 + b1)
                x = x @ w2 + b2
                x_seq[-1:] = x_seq[-1:] + x  # Residual FFN

            logit = x_seq[-1:] @ self.out_w + self.out_b
            logits.append(logit)

        return np.vstack(logits)
    
    def backward(self, logits, targets, saved_ffn_inputs):
        """
        logits: (T, vocab_size)
        targets: (T,)
        saved_ffn_inputs: list of (x_pre_ffn, w1, w2) for each layer
        """
        probs = np.exp(logits - np.max(logits, axis=1, keepdims=True))
        probs /= np.sum(probs, axis=1, keepdims=True)

        probs[np.arange(len(targets)), targets] -= 1  # dL/dlogits
        dlogits = probs / len(targets)

        # Output layer grads
        x_last = saved_ffn_inputs[-1][0]  # last layer input to output projection
        self.grad_out_w += x_last.T @ dlogits
        self.grad_out_b += dlogits.sum(axis=0)

        # FFN backward pass (simple, no attention backprop yet)
        for i, (x, w1, w2) in reversed(list(enumerate(saved_ffn_inputs))):
            # dL/dx after output
            dout = dlogits @ self.out_w.T

            # FFN second layer
            dff2 = dout @ w2.T
            self.grad_accum['ffn'][i][2] += (np.tanh(x @ w1) @ w2).T @ dout
            self.grad_accum['ffn'][i][3] += dout.sum(axis=0)

            # FFN first layer
            dtanh = 1 - np.tanh(x @ w1) ** 2
            dff1 = (dout @ w2.T) * dtanh
            self.grad_accum['ffn'][i][0] += x.T @ dff1
            self.grad_accum['ffn'][i][1] += dff1.sum(axis=0)

    def apply_gradients(self, lr, accum_steps):
        self.out_w -= lr * self.grad_out_w / accum_steps
        self.out_b -= lr * self.grad_out_b / accum_steps

        for i, grads in enumerate(self.grad_accum['ffn']):
            dw1, db1, dw2, db2 = grads
            w1, b1 = self.ffn_layers[i][0]
            w2, b2 = self.ffn_layers[i][1]

            self.ffn_layers[i] = (
                (w1 - lr * dw1 / accum_steps, b1 - lr * db1 / accum_steps),
                (w2 - lr * dw2 / accum_steps, b2 - lr * db2 / accum_steps)
            )

        # Zero accumulators
        self.grad_out_w.fill(0)
        self.grad_out_b.fill(0)
        for grads in self.grad_accum['ffn']:
            for g in grads:
                g.fill(0)


