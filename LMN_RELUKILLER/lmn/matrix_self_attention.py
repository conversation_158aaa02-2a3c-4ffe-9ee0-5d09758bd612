import numpy as np

class MatrixSelfAttention:
    def __init__(self, d_model=256, n_heads=8):
        assert d_model % n_heads == 0, "d_model must be divisible by n_heads"
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_head = d_model // n_heads

        self.Wq = np.random.randn(d_model, d_model) * np.sqrt(2.0 / d_model)
        self.Wk = np.random.randn(d_model, d_model) * np.sqrt(2.0 / d_model)
        self.Wv = np.random.randn(d_model, d_model) * np.sqrt(2.0 / d_model)
        self.Wo = np.random.randn(d_model, d_model) * np.sqrt(2.0 / d_model)

    def _split_heads(self, x):
        T = x.shape[0]
        return x.reshape(T, self.n_heads, self.d_head).transpose(1, 0, 2)

    def _combine_heads(self, x_heads):
        T = x_heads.shape[1]
        return x_heads.transpose(1, 0, 2).reshape(T, self.d_model)

    def _masked_softmax(self, scores):
        T = scores.shape[-1]
        mask = np.triu(np.ones((T, T)), k=1).astype(bool)
        scores = np.where(mask[None, :, :], -1e9, scores)
        exp = np.exp(scores - np.max(scores, axis=-1, keepdims=True))
        return exp / np.sum(exp, axis=-1, keepdims=True)

    def forward(self, x):
        Q = x @ self.Wq
        K = x @ self.Wk
        V = x @ self.Wv

        Q_heads = self._split_heads(Q)
        K_heads = self._split_heads(K)
        V_heads = self._split_heads(V)

        scores = Q_heads @ K_heads.transpose(0, 2, 1) / np.sqrt(self.d_head)
        weights = self._masked_softmax(scores)
        attention = weights @ V_heads

        concat = self._combine_heads(attention)
        return concat @ self.Wo
    
    def forward_to_context(self, x, context):
        Q = x @ self.Wq
        K = context @ self.Wk
        V = context @ self.Wv

        Q_heads = self._split_heads(Q)
        K_heads = self._split_heads(K)
        V_heads = self._split_heads(V)

        scores = Q_heads @ K_heads.transpose(0, 2, 1) / np.sqrt(self.d_head)
        weights = self._masked_softmax(scores)  # optional mask for padding
        attention = weights @ V_heads

        concat = self._combine_heads(attention)
        return concat @ self.Wo

