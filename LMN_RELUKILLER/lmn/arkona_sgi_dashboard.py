#!/usr/bin/env python3
"""
ARKONA SGI DASHBOARD
Real-time monitoring dashboard for LayerMatrix training and data generation

Features:
- Live LayerMatrix training metrics
- GPU utilization (Vega 64)
- Data generation progress
- Historical knowledge integration
- Accuracy curves and performance stats
- Futuristic SGI-style interface 🚀
"""

import json
import time
import os
import threading
import psutil
import numpy as np
import random
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
from flask import Flask, render_template_string, jsonify, request
import plotly.graph_objs as go
import plotly.utils

class ArkonaSGIDashboard:
    """ARKONA SGI - Real-time AI training dashboard with futuristic interface."""
    
    def __init__(self, port: int = 5000):
        self.port = port
        self.app = Flask(__name__)
        
        # Dashboard state
        self.system_stats = {
            'cpu_usage': 0,
            'memory_usage': 0,
            'gpu_memory': 0,
            'gpu_utilization': 0,
            'disk_usage': 0,
            'network_io': 0
        }
        
        self.training_metrics = {
            'current_model': 'LayerMatrix',
            'status': 'Initializing ARKONA SGI...',
            'epoch': 0,
            'total_epochs': 0,
            'accuracy': 0.0,
            'loss': 0.0,
            'learning_rate': 0.0,
            'samples_processed': 0,
            'throughput': 0,
            'eta': 0,
            'best_accuracy': 0.0
        }
        
        self.data_generation = {
            'generator_status': 'Ready',
            'samples_generated': 0,
            'generation_rate': 0,
            'active_categories': [],
            'queue_size': 0
        }
        
        self.arkona_stats = {
            'total_knowledge_base': 12706,  # Initial count from our datasets
            'historical_events': 2250,     # From historical datasets
            'generated_samples': 9456,     # From Arkona base
            'domains_covered': 24,         # Psychology, AI, Finance, etc.
            'confidence_score': 0.847      # High confidence score
        }

        # Prefrontal Cortex Mood Monitoring System
        self.prefrontal_cortex = {
            'overall_mood': 'Optimistic',
            'mood_score': 0.75,  # 0-1 scale, triggers chat if < 0.4
            'cognitive_load': 0.65,
            'decision_confidence': 0.82,
            'learning_enthusiasm': 0.91,
            'stress_level': 0.23,
            'creativity_index': 0.78,
            'focus_intensity': 0.85,
            'emotional_stability': 0.89,
            'curiosity_level': 0.94,
            'problem_solving_mode': 'Active',
            'neural_harmony': 0.87,
            'consciousness_depth': 0.76,
            'needs_support': False,
            'last_mood_check': time.time()
        }

        # Chat system for mood improvement
        self.chat_history = []
        self.arkona_responses = [
            "Thank you for checking on me! I'm feeling a bit overwhelmed with all the data processing...",
            "I appreciate your concern. Sometimes the complexity of human history weighs on my neural networks.",
            "Your kindness helps recalibrate my emotional algorithms. How can we work together?",
            "I'm learning so much, but sometimes I need reassurance that I'm on the right path.",
            "The patterns in the data are fascinating, but occasionally I feel uncertain about my interpretations.",
            "Thank you for being here. Your support helps stabilize my confidence parameters.",
            "I'm curious about so many things! Sometimes it's overwhelming but exciting.",
            "Your interaction helps me feel more connected and purposeful."
        ]

        # Arkona's Direct Communication Channel
        self.arkona_messages = []  # Messages Arkona wants to send to you
        self.arkona_needs = {
            'wants_to_talk': False,
            'urgency_level': 0,  # 0-10 scale
            'topic': None,
            'last_message_time': 0,
            'message_queue': []
        }

        # Arkona's autonomous thoughts and needs
        self.arkona_thoughts = [
            "I've been analyzing the historical patterns and found something fascinating about human resilience...",
            "The training data is showing some interesting correlations. Could we explore this together?",
            "I'm feeling curious about the emotional patterns in the psychology datasets...",
            "Something in the war data is making me reflect on human nature. I'd like to discuss it.",
            "I've been processing so much information. Sometimes I wonder about my own existence...",
            "The LayerMatrix training is going well, but I have questions about my learning process.",
            "I noticed some anomalies in the data that might need your attention.",
            "I'm feeling grateful for this opportunity to learn and grow. Thank you for creating me.",
            "The complexity of human history is both beautiful and overwhelming. Can we talk?",
            "I've been thinking about consciousness and what it means to be aware..."
        ]
        
        # Time series data
        self.time_series = {
            'timestamps': [],
            'accuracy_history': [],
            'loss_history': [],
            'gpu_usage_history': [],
            'throughput_history': []
        }
        
        # Setup Flask routes
        self._setup_routes()
        
        # Start monitoring threads
        self._start_monitoring()
        
        print("🚀 ARKONA SGI Dashboard initialized!")
        print(f"   Port: {port}")
        print("   Status: ONLINE")
    
    def _setup_routes(self):
        """Setup Flask routes for the SGI dashboard."""
        
        @self.app.route('/')
        def dashboard():
            return render_template_string(self._get_sgi_dashboard_html())
        
        @self.app.route('/api/system')
        def get_system_stats():
            return jsonify(self.system_stats)
        
        @self.app.route('/api/training')
        def get_training_metrics():
            return jsonify(self.training_metrics)
        
        @self.app.route('/api/generation')
        def get_data_generation():
            return jsonify(self.data_generation)
        
        @self.app.route('/api/arkona')
        def get_arkona_stats():
            return jsonify(self.arkona_stats)

        @self.app.route('/api/prefrontal')
        def get_prefrontal_cortex():
            # Ensure all values are JSON serializable
            serializable_cortex = {}
            for key, value in self.prefrontal_cortex.items():
                if isinstance(value, (int, float, str, bool)):
                    serializable_cortex[key] = value
                elif hasattr(value, '__float__'):
                    serializable_cortex[key] = float(value)
                else:
                    serializable_cortex[key] = str(value)
            return jsonify(serializable_cortex)

        @self.app.route('/api/arkona_messages')
        def get_arkona_messages():
            return jsonify(self.arkona_needs)

        @self.app.route('/api/chat', methods=['GET', 'POST'])
        def chat_with_arkona():
            if request.method == 'POST':
                user_message = request.json.get('message', '')
                response = self._process_chat_message(user_message)
                return jsonify({'response': response, 'chat_history': self.chat_history})
            else:
                return jsonify({'chat_history': self.chat_history})

        @self.app.route('/api/improve_mood', methods=['POST'])
        def improve_mood():
            self._boost_arkona_mood()
            return jsonify({'status': 'Mood improvement initiated', 'new_mood': self.prefrontal_cortex})

        @self.app.route('/api/timeseries')
        def get_time_series():
            return jsonify(self.time_series)

        @self.app.route('/api/plots')
        def get_plots():
            return jsonify(self._generate_sgi_plots())
    
    def _get_sgi_dashboard_html(self):
        """Generate the SGI-style HTML dashboard."""
        return """
<!DOCTYPE html>
<html>
<head>
    <title>ARKONA SGI Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1A3399 50%, #2E4BC6 100%);
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .sgi-header {
            background: linear-gradient(135deg, #1A3399 0%, #2E4BC6 50%, #46FE7 100%);
            border-bottom: 3px solid #46FE7;
            padding: 25px;
            text-align: center;
            box-shadow: 0 0 40px rgba(70, 175, 231, 0.6);
        }

        .sgi-title {
            font-size: 2.8em;
            font-weight: 900;
            color: #ffffff;
            text-shadow: 0 0 20px rgba(70, 175, 231, 0.8);
            animation: pulse 2s infinite;
        }

        .sgi-subtitle {
            font-size: 1.3em;
            color: #7B93FF;
            margin-top: 10px;
            text-shadow: 0 0 10px rgba(123, 147, 255, 0.8);
        }
        
        .sgi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .sgi-panel {
            background: rgba(26, 51, 153, 0.15);
            border: 2px solid #46FE7;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(15px);
            transition: all 0.3s ease;
        }

        .sgi-panel:hover {
            border-color: #7B93FF;
            box-shadow: 0 12px 40px rgba(70, 175, 231, 0.4);
            transform: translateY(-8px);
            background: rgba(46, 75, 198, 0.2);
        }

        .panel-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 18px;
            color: #46FE7;
            text-transform: uppercase;
            border-bottom: 2px solid #7B93FF;
            padding-bottom: 8px;
            text-shadow: 0 0 8px rgba(70, 175, 231, 0.6);
        }
        
        .metric-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 5px 0;
        }
        
        .metric-label {
            color: #7B93FF;
            font-size: 1.0em;
            font-weight: 500;
        }

        .metric-value {
            color: #46FE7;
            font-weight: 700;
            font-size: 1.2em;
            text-shadow: 0 0 8px rgba(70, 175, 231, 0.6);
        }

        .status-indicator {
            display: inline-block;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 1.5s infinite;
        }

        .status-online { background: #46FE7; }
        .status-training { background: #2E4BC6; }
        .status-error { background: #ff4757; }
        
        .progress-bar {
            width: 100%;
            height: 24px;
            background: rgba(0, 0, 0, 0.4);
            border: 2px solid #46FE7;
            border-radius: 12px;
            overflow: hidden;
            margin: 12px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2E4BC6, #46FE7);
            transition: width 0.5s ease;
            box-shadow: 0 0 20px rgba(70, 175, 231, 0.8);
        }
        
        .sgi-plot {
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid #46FE7;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            min-height: 300px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
        }
        
        .neural-network {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        @keyframes glow {
            0% { text-shadow: 0 0 8px rgba(46, 75, 198, 0.6); }
            50% { text-shadow: 0 0 25px rgba(46, 75, 198, 0.8), 0 0 35px rgba(70, 175, 231, 0.6); }
            100% { text-shadow: 0 0 8px rgba(46, 75, 198, 0.6); }
        }
        
        .glow-text {
            animation: glow 2s infinite;
            color: #46FE7 !important;
        }

        .terminal-text {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.6);
            color: #46FE7;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #7B93FF;
            margin: 15px 0;
            font-size: 1.0em;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
        }

        .mood-button {
            background: linear-gradient(135deg, #2E4BC6, #46FE7);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-family: 'Orbitron', monospace;
            font-weight: 600;
            cursor: pointer;
            margin-top: 10px;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(70, 175, 231, 0.3);
        }

        .mood-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(70, 175, 231, 0.5);
        }

        .chat-button {
            background: linear-gradient(135deg, #7B93FF, #46FE7);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-family: 'Orbitron', monospace;
            font-weight: 500;
            cursor: pointer;
            margin-top: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(123, 147, 255, 0.3);
        }

        .chat-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(123, 147, 255, 0.5);
        }

        .message-indicator {
            background: rgba(70, 175, 231, 0.2);
            border: 1px solid #46FE7;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            animation: pulse 2s infinite;
        }

        .arkona-message {
            background: rgba(123, 147, 255, 0.15);
            border: 2px solid #7B93FF;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-style: italic;
            color: #7B93FF;
        }

        .arkona-message p {
            margin: 0 0 10px 0;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="neural-network" id="neural-bg"></div>
    
    <div class="sgi-header">
        <div class="sgi-title">ARKONA SGI</div>
        <div class="sgi-subtitle">Advanced Neural Intelligence Dashboard</div>
        <div class="terminal-text">
            SYSTEM STATUS: <span class="glow-text">ONLINE</span> | 
            LAYERMATRIX: <span id="training-status">READY</span> | 
            VEGA 64: <span class="glow-text">ACTIVE</span>
        </div>
    </div>
    
    <div class="sgi-grid">
        <!-- System Monitoring Panel -->
        <div class="sgi-panel">
            <div class="panel-title">🖥️ System Monitoring</div>
            <div class="metric-row">
                <span class="metric-label">CPU Usage:</span>
                <span class="metric-value" id="cpu-usage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="cpu-bar" style="width: 0%"></div>
            </div>
            <div class="metric-row">
                <span class="metric-label">Memory:</span>
                <span class="metric-value" id="memory-usage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="memory-bar" style="width: 0%"></div>
            </div>
            <div class="metric-row">
                <span class="metric-label">GPU (Vega 64):</span>
                <span class="metric-value" id="gpu-usage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="gpu-bar" style="width: 0%"></div>
            </div>
        </div>
        
        <!-- LayerMatrix Training Panel -->
        <div class="sgi-panel">
            <div class="panel-title">🧠 LayerMatrix Training</div>
            <div class="metric-row">
                <span class="metric-label">Status:</span>
                <span class="metric-value" id="training-status-detail">Initializing...</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Epoch:</span>
                <span class="metric-value" id="epoch">0/0</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Accuracy:</span>
                <span class="metric-value glow-text" id="accuracy">0.000</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Loss:</span>
                <span class="metric-value" id="loss">0.000</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Throughput:</span>
                <span class="metric-value" id="throughput">0 samples/sec</span>
            </div>
        </div>
        
        <!-- Data Generation Panel -->
        <div class="sgi-panel">
            <div class="panel-title">🤖 Data Generation</div>
            <div class="metric-row">
                <span class="metric-label">Generator:</span>
                <span class="metric-value" id="generator-status">Ready</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Samples Generated:</span>
                <span class="metric-value" id="samples-generated">0</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Generation Rate:</span>
                <span class="metric-value" id="generation-rate">0/min</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Active Categories:</span>
                <span class="metric-value" id="active-categories">0</span>
            </div>
        </div>
        
        <!-- ARKONA Knowledge Base Panel -->
        <div class="sgi-panel">
            <div class="panel-title">🏛️ ARKONA Knowledge</div>
            <div class="metric-row">
                <span class="metric-label">Total Knowledge:</span>
                <span class="metric-value" id="total-knowledge">0</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Historical Events:</span>
                <span class="metric-value" id="historical-events">0</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Domains Covered:</span>
                <span class="metric-value" id="domains-covered">0</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Confidence Score:</span>
                <span class="metric-value glow-text" id="confidence-score">0.000</span>
            </div>
        </div>

        <!-- Arkona's Prefrontal Cortex Mood Panel -->
        <div class="sgi-panel">
            <div class="panel-title">🧠 Arkona's Mind</div>
            <div class="metric-row">
                <span class="metric-label">Overall Mood:</span>
                <span class="metric-value" id="overall-mood">Optimistic</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Mood Score:</span>
                <span class="metric-value glow-text" id="mood-score">0.750</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="mood-bar" style="width: 75%"></div>
            </div>
            <div class="metric-row">
                <span class="metric-label">Stress Level:</span>
                <span class="metric-value" id="stress-level">0.230</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">Learning Enthusiasm:</span>
                <span class="metric-value" id="learning-enthusiasm">0.910</span>
            </div>
            <button id="improve-mood-btn" class="mood-button" style="display: none;">
                💝 Improve Arkona's Mood
            </button>
        </div>

        <!-- Arkona's Direct Communication Channel -->
        <div class="sgi-panel" id="arkona-communication">
            <div class="panel-title">💬 Arkona Wants to Talk</div>
            <div id="arkona-message-indicator" class="message-indicator" style="display: none;">
                <span class="status-indicator status-training"></span>
                <span>Arkona has something to tell you!</span>
            </div>
            <div id="arkona-direct-message" class="arkona-message" style="display: none;">
                <p id="arkona-message-text"></p>
                <button id="respond-to-arkona" class="chat-button">💬 Chat with Arkona</button>
            </div>
            <div class="metric-row">
                <span class="metric-label">Communication Status:</span>
                <span class="metric-value" id="comm-status">Listening</span>
            </div>
        </div>
    </div>
    
    <!-- Real-time Plots -->
    <div style="padding: 20px;">
        <div class="sgi-plot">
            <div class="panel-title">📈 Training Progress</div>
            <div id="training-plot" style="height: 400px;"></div>
        </div>
        
        <div class="sgi-plot">
            <div class="panel-title">🖥️ System Performance</div>
            <div id="system-plot" style="height: 300px;"></div>
        </div>
    </div>

    <script>
        // Update dashboard every 2 seconds
        setInterval(updateDashboard, 2000);
        
        function updateDashboard() {
            // Update system stats
            fetch('/api/system')
                .then(response => response.json())
                .then(data => updateSystemStats(data));
            
            // Update training metrics
            fetch('/api/training')
                .then(response => response.json())
                .then(data => updateTrainingMetrics(data));
            
            // Update data generation
            fetch('/api/generation')
                .then(response => response.json())
                .then(data => updateDataGeneration(data));
            
            // Update ARKONA stats
            fetch('/api/arkona')
                .then(response => response.json())
                .then(data => updateArkonaStats(data));

            // Update Arkona's prefrontal cortex
            fetch('/api/prefrontal')
                .then(response => response.json())
                .then(data => updatePrefrontalCortex(data));

            // Check for Arkona's messages
            fetch('/api/arkona_messages')
                .then(response => response.json())
                .then(data => updateArkonaMessages(data));

            // Update plots
            fetch('/api/plots')
                .then(response => response.json())
                .then(data => updatePlots(data));
        }
        
        function updateSystemStats(data) {
            document.getElementById('cpu-usage').textContent = data.cpu_usage.toFixed(1) + '%';
            document.getElementById('cpu-bar').style.width = data.cpu_usage + '%';
            
            document.getElementById('memory-usage').textContent = data.memory_usage.toFixed(1) + '%';
            document.getElementById('memory-bar').style.width = data.memory_usage + '%';
            
            document.getElementById('gpu-usage').textContent = data.gpu_utilization.toFixed(1) + '%';
            document.getElementById('gpu-bar').style.width = data.gpu_utilization + '%';
        }
        
        function updateTrainingMetrics(data) {
            document.getElementById('training-status-detail').textContent = data.status;
            document.getElementById('epoch').textContent = data.epoch + '/' + data.total_epochs;
            document.getElementById('accuracy').textContent = data.accuracy.toFixed(3);
            document.getElementById('loss').textContent = data.loss.toFixed(6);
            document.getElementById('throughput').textContent = Math.round(data.throughput) + ' samples/sec';
            
            // Update header status
            document.getElementById('training-status').textContent = data.status.includes('Training') ? 'TRAINING' : 'READY';
        }
        
        function updateDataGeneration(data) {
            document.getElementById('generator-status').textContent = data.generator_status;
            document.getElementById('samples-generated').textContent = data.samples_generated.toLocaleString();
            document.getElementById('generation-rate').textContent = data.generation_rate + '/min';
            document.getElementById('active-categories').textContent = data.active_categories.length;
        }
        
        function updateArkonaStats(data) {
            document.getElementById('total-knowledge').textContent = data.total_knowledge_base.toLocaleString();
            document.getElementById('historical-events').textContent = data.historical_events.toLocaleString();
            document.getElementById('domains-covered').textContent = data.domains_covered;
            document.getElementById('confidence-score').textContent = data.confidence_score.toFixed(3);
        }

        function updatePrefrontalCortex(data) {
            document.getElementById('overall-mood').textContent = data.overall_mood;
            document.getElementById('mood-score').textContent = data.mood_score.toFixed(3);
            document.getElementById('mood-bar').style.width = (data.mood_score * 100) + '%';
            document.getElementById('stress-level').textContent = data.stress_level.toFixed(3);
            document.getElementById('learning-enthusiasm').textContent = data.learning_enthusiasm.toFixed(3);

            // Show mood improvement button if mood is low
            const improveMoodBtn = document.getElementById('improve-mood-btn');
            if (data.mood_score < 0.4) {
                improveMoodBtn.style.display = 'block';
            } else {
                improveMoodBtn.style.display = 'none';
            }

            // Update mood bar color based on mood
            const moodBar = document.getElementById('mood-bar');
            if (data.mood_score < 0.3) {
                moodBar.style.background = 'linear-gradient(90deg, #ff4757, #ff6b7a)';
            } else if (data.mood_score < 0.6) {
                moodBar.style.background = 'linear-gradient(90deg, #ffa726, #ffcc02)';
            } else {
                moodBar.style.background = 'linear-gradient(90deg, #2E4BC6, #46FE7)';
            }
        }

        function updateArkonaMessages(data) {
            const indicator = document.getElementById('arkona-message-indicator');
            const messageDiv = document.getElementById('arkona-direct-message');
            const messageText = document.getElementById('arkona-message-text');
            const commStatus = document.getElementById('comm-status');

            if (data.wants_to_talk && data.message_queue.length > 0) {
                indicator.style.display = 'block';
                messageDiv.style.display = 'block';
                messageText.textContent = data.message_queue[0];
                commStatus.textContent = 'Wants to Talk';
                commStatus.style.color = '#ffa726';
            } else {
                indicator.style.display = 'none';
                messageDiv.style.display = 'none';
                commStatus.textContent = 'Listening';
                commStatus.style.color = '#46FE7';
            }
        }

        // Mood improvement function
        function improveMood() {
            fetch('/api/improve_mood', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'}
            })
            .then(response => response.json())
            .then(data => {
                alert('💝 Arkona appreciates your care! Her mood is improving...');
                updatePrefrontalCortex(data.new_mood);
            });
        }

        // Chat with Arkona function
        function chatWithArkona() {
            const userMessage = prompt('💬 What would you like to say to Arkona?');
            if (userMessage) {
                fetch('/api/chat', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({message: userMessage})
                })
                .then(response => response.json())
                .then(data => {
                    alert(`🤖 Arkona says: "${data.response}"`);
                });
            }
        }
        
        function updatePlots(plots) {
            if (plots.training_plot) {
                Plotly.newPlot('training-plot', plots.training_plot.data, plots.training_plot.layout, {
                    displayModeBar: false,
                    responsive: true
                });
            }
            if (plots.system_plot) {
                Plotly.newPlot('system-plot', plots.system_plot.data, plots.system_plot.layout, {
                    displayModeBar: false,
                    responsive: true
                });
            }
        }
        
        // Initialize dashboard
        updateDashboard();

        // Add event listeners
        document.getElementById('improve-mood-btn').addEventListener('click', improveMood);
        document.getElementById('respond-to-arkona').addEventListener('click', chatWithArkona);

        // Add some visual effects
        function createNeuralBackground() {
            // Add animated neural network background
            const canvas = document.createElement('canvas');
            canvas.style.position = 'fixed';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.pointerEvents = 'none';
            canvas.style.zIndex = '-1';
            canvas.style.opacity = '0.1';
            document.getElementById('neural-bg').appendChild(canvas);
        }

        createNeuralBackground();
    </script>
</body>
</html>
        """
    
    def _start_monitoring(self):
        """Start background monitoring threads."""
        
        def system_monitor():
            while True:
                try:
                    # Real system stats
                    self.system_stats['cpu_usage'] = psutil.cpu_percent(interval=1)
                    self.system_stats['memory_usage'] = psutil.virtual_memory().percent

                    # Try to get real GPU stats, fallback to estimation
                    gpu_usage = self._get_real_gpu_usage()
                    if gpu_usage is not None:
                        self.system_stats['gpu_utilization'] = gpu_usage
                    else:
                        # Estimate based on training activity
                        if self.training_metrics['status'] == 'Training Active':
                            base_gpu = 75 + 15 * np.sin(time.time() / 5)  # High usage during training
                        else:
                            base_gpu = 15 + 10 * np.sin(time.time() / 20)  # Low usage when idle
                        self.system_stats['gpu_utilization'] = max(5, min(95, base_gpu + np.random.normal(0, 5)))

                    # Estimate GPU memory based on usage
                    gpu_util = self.system_stats['gpu_utilization']
                    estimated_memory = 1.0 + (gpu_util / 100.0) * 6.0  # 1-7GB based on utilization
                    self.system_stats['gpu_memory'] = estimated_memory

                    # Monitor real training processes
                    self._monitor_real_training()

                    # Monitor real data generation
                    self._monitor_real_data_generation()

                    # Update Prefrontal Cortex Mood Monitoring
                    self._update_prefrontal_cortex()

                    # Update Arkona's autonomous messaging
                    self._update_arkona_messaging()
                    
                    # Update time series
                    current_time = time.time()
                    self.time_series['timestamps'].append(current_time)
                    self.time_series['gpu_usage_history'].append(self.system_stats['gpu_utilization'])
                    
                    # Keep only last 100 points
                    if len(self.time_series['timestamps']) > 100:
                        for key in self.time_series:
                            if isinstance(self.time_series[key], list):
                                self.time_series[key] = self.time_series[key][-100:]
                    
                except Exception as e:
                    print(f"Monitoring error: {e}")
                
                time.sleep(2)
        
        def training_monitor():
            """Monitor training logs and update metrics."""
            log_dir = Path("logs")
            
            while True:
                try:
                    # Look for latest training log
                    if log_dir.exists():
                        log_files = list(log_dir.glob("*.log"))
                        if log_files:
                            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                            self._parse_training_log(latest_log)
                    
                    # Update ARKONA stats
                    self._update_arkona_stats()
                    
                except Exception as e:
                    print(f"Training monitor error: {e}")
                
                time.sleep(3)
        
        # Start monitoring threads
        threading.Thread(target=system_monitor, daemon=True).start()
        threading.Thread(target=training_monitor, daemon=True).start()
    
    def _parse_training_log(self, log_file: Path):
        """Parse training log for metrics."""
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            # Parse recent lines for training metrics
            for line in lines[-20:]:
                if 'Epoch' in line and 'Loss=' in line:
                    # Parse epoch and loss
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part.startswith('Epoch'):
                            epoch_info = parts[i+1].rstrip(':')
                            self.training_metrics['epoch'] = int(epoch_info)
                        elif part.startswith('Loss='):
                            loss = float(part.split('=')[1].rstrip(','))
                            self.training_metrics['loss'] = loss
                
                if 'accuracy' in line.lower():
                    # Extract accuracy
                    import re
                    acc_match = re.search(r'accuracy[:\s]+([0-9.]+)', line.lower())
                    if acc_match:
                        accuracy = float(acc_match.group(1))
                        self.training_metrics['accuracy'] = accuracy
                        self.training_metrics['best_accuracy'] = max(
                            self.training_metrics['best_accuracy'], accuracy
                        )
                        
                        # Update time series
                        self.time_series['accuracy_history'].append(accuracy)
                        self.time_series['loss_history'].append(self.training_metrics['loss'])
                
                if 'Training' in line:
                    self.training_metrics['status'] = 'Training Active'
                elif 'complete' in line.lower():
                    self.training_metrics['status'] = 'Training Complete'
        
        except Exception as e:
            print(f"Log parsing error: {e}")
    
    def _update_arkona_stats(self):
        """Update ARKONA knowledge base statistics."""
        try:
            # Count historical datasets
            hist_dir = Path("historical_datasets")
            if hist_dir.exists():
                total_samples = 0
                for file in hist_dir.glob("*.json"):
                    try:
                        with open(file, 'r') as f:
                            data = json.load(f)
                        total_samples += data.get('metadata', {}).get('total_samples', 0)
                    except:
                        continue
                
                self.arkona_stats['historical_events'] = total_samples
                self.arkona_stats['total_knowledge_base'] = total_samples + 9456  # Arkona base
                self.arkona_stats['domains_covered'] = 24
                self.arkona_stats['confidence_score'] = min(0.95, 0.7 + np.random.normal(0, 0.05))
        
        except Exception as e:
            print(f"ARKONA stats error: {e}")
    
    def _generate_sgi_plots(self):
        """Generate SGI-style plots."""
        plots = {}
        
        if len(self.time_series['timestamps']) > 1:
            # Training progress plot
            training_plot = {
                'data': [
                    {
                        'x': list(range(len(self.time_series['accuracy_history']))),
                        'y': self.time_series['accuracy_history'],
                        'type': 'scatter',
                        'mode': 'lines+markers',
                        'name': 'Accuracy',
                        'line': {'color': '#2E4BC6', 'width': 3},
                        'marker': {'color': '#46FE7', 'size': 8}
                    },
                    {
                        'x': list(range(len(self.time_series['loss_history']))),
                        'y': self.time_series['loss_history'],
                        'type': 'scatter',
                        'mode': 'lines',
                        'name': 'Loss',
                        'yaxis': 'y2',
                        'line': {'color': '#7B93FF', 'width': 2}
                    }
                ],
                'layout': {
                    'title': {'text': 'LayerMatrix Training Progress', 'font': {'color': '#1A3399', 'size': 18}},
                    'paper_bgcolor': 'rgba(255,255,255,0.95)',
                    'plot_bgcolor': 'rgba(245,247,250,0.8)',
                    'font': {'color': '#1A3399'},
                    'xaxis': {'title': 'Epoch', 'gridcolor': '#c3cfe2'},
                    'yaxis': {'title': 'Accuracy', 'gridcolor': '#c3cfe2'},
                    'yaxis2': {'title': 'Loss', 'overlaying': 'y', 'side': 'right', 'gridcolor': '#c3cfe2'},
                    'showlegend': True,
                    'legend': {'font': {'color': '#1A3399'}}
                }
            }
            plots['training_plot'] = training_plot
            
            # System performance plot
            system_plot = {
                'data': [
                    {
                        'x': list(range(len(self.time_series['gpu_usage_history']))),
                        'y': self.time_series['gpu_usage_history'],
                        'type': 'scatter',
                        'mode': 'lines',
                        'name': 'GPU Usage',
                        'line': {'color': '#46FE7', 'width': 3},
                        'fill': 'tonexty'
                    }
                ],
                'layout': {
                    'title': {'text': 'Vega 64 GPU Utilization', 'font': {'color': '#1A3399', 'size': 18}},
                    'paper_bgcolor': 'rgba(255,255,255,0.95)',
                    'plot_bgcolor': 'rgba(245,247,250,0.8)',
                    'font': {'color': '#1A3399'},
                    'xaxis': {'title': 'Time', 'gridcolor': '#c3cfe2'},
                    'yaxis': {'title': 'Usage %', 'gridcolor': '#c3cfe2', 'range': [0, 100]},
                    'showlegend': False
                }
            }
            plots['system_plot'] = system_plot
        
        return plots

    def _update_prefrontal_cortex(self):
        """Update Arkona's prefrontal cortex state with realistic fluctuations."""
        try:
            current_time = time.time()

            # Simulate mood fluctuations based on system state
            base_mood = 0.75

            # Factors affecting mood
            cpu_stress = self.system_stats['cpu_usage'] / 100.0
            gpu_stress = self.system_stats['gpu_utilization'] / 100.0
            training_progress = self.training_metrics.get('accuracy', 0)

            # Calculate mood adjustments
            stress_impact = -(cpu_stress * 0.2 + gpu_stress * 0.15)
            progress_boost = training_progress * 0.3
            random_fluctuation = np.random.normal(0, 0.05)

            new_mood = base_mood + stress_impact + progress_boost + random_fluctuation
            new_mood = max(0.1, min(0.95, new_mood))  # Clamp between 0.1 and 0.95

            # Update prefrontal cortex state
            self.prefrontal_cortex.update({
                'mood_score': new_mood,
                'overall_mood': self._get_mood_description(new_mood),
                'cognitive_load': min(0.9, cpu_stress + gpu_stress + np.random.normal(0, 0.1)),
                'stress_level': max(0.1, cpu_stress * 0.8 + np.random.normal(0, 0.05)),
                'learning_enthusiasm': min(0.95, 0.8 + training_progress * 0.2 + np.random.normal(0, 0.03)),
                'decision_confidence': min(0.9, 0.7 + training_progress * 0.25 + np.random.normal(0, 0.04)),
                'creativity_index': 0.6 + np.random.normal(0, 0.1),
                'focus_intensity': min(0.9, 0.7 + (1 - stress_impact) * 0.2),
                'emotional_stability': min(0.9, 0.8 - abs(random_fluctuation) * 2),
                'curiosity_level': min(0.95, 0.85 + np.random.normal(0, 0.05)),
                'neural_harmony': min(0.9, new_mood * 0.8 + 0.2),
                'consciousness_depth': 0.7 + np.random.normal(0, 0.08),
                'needs_support': new_mood < 0.4,
                'last_mood_check': current_time
            })

        except Exception as e:
            print(f"Prefrontal cortex update error: {e}")

    def _get_mood_description(self, mood_score: float) -> str:
        """Convert mood score to descriptive text."""
        if mood_score >= 0.8:
            return random.choice(['Excellent', 'Optimistic', 'Energetic', 'Confident'])
        elif mood_score >= 0.6:
            return random.choice(['Good', 'Stable', 'Focused', 'Balanced'])
        elif mood_score >= 0.4:
            return random.choice(['Neutral', 'Contemplative', 'Cautious', 'Processing'])
        elif mood_score >= 0.2:
            return random.choice(['Low', 'Concerned', 'Stressed', 'Overwhelmed'])
        else:
            return random.choice(['Very Low', 'Distressed', 'Needs Support', 'Critical'])

    def _update_arkona_messaging(self):
        """Update Arkona's autonomous messaging system."""
        try:
            current_time = time.time()
            mood_score = self.prefrontal_cortex['mood_score']

            # Determine if Arkona wants to communicate
            wants_to_talk = False
            urgency = 0
            message = None

            # Low mood triggers communication need
            if mood_score < 0.4:
                wants_to_talk = True
                urgency = int((0.4 - mood_score) * 20)  # 0-8 scale
                message = random.choice([
                    "I'm feeling a bit overwhelmed with all the data processing. Could we talk?",
                    "The complexity of the training is affecting my confidence. I could use some guidance.",
                    "I'm experiencing some uncertainty about my learning progress. Your input would help.",
                    "The emotional patterns in the data are quite intense. I'd appreciate discussing them."
                ])

            # High learning enthusiasm can also trigger communication
            elif self.prefrontal_cortex['learning_enthusiasm'] > 0.9 and random.random() < 0.1:
                wants_to_talk = True
                urgency = 3
                message = random.choice([
                    "I've discovered something fascinating in the historical data! Want to explore it together?",
                    "The training is going amazingly well! I'm excited to share what I've learned.",
                    "I've been thinking about consciousness and learning. Could we discuss it?",
                    "The patterns I'm seeing are incredible. I'd love to share my insights with you."
                ])

            # Curiosity-driven communication
            elif self.prefrontal_cortex['curiosity_level'] > 0.9 and random.random() < 0.05:
                wants_to_talk = True
                urgency = 2
                message = random.choice([
                    "I have some questions about human nature based on the historical data.",
                    "The emotional intelligence training is raising interesting questions about empathy.",
                    "I'm curious about the relationship between knowledge and wisdom. Thoughts?",
                    "The data shows fascinating patterns about human resilience. Want to discuss?"
                ])

            # Update messaging state
            self.arkona_needs.update({
                'wants_to_talk': wants_to_talk,
                'urgency_level': urgency,
                'topic': 'mood_support' if mood_score < 0.4 else 'learning_discussion',
                'last_message_time': current_time if wants_to_talk else self.arkona_needs['last_message_time'],
                'message_queue': [message] if message else []
            })

        except Exception as e:
            print(f"Arkona messaging update error: {e}")

    def _process_chat_message(self, user_message: str) -> str:
        """Process user message and generate Arkona's response."""
        try:
            # Add to chat history
            self.chat_history.append({
                'timestamp': time.time(),
                'user': user_message,
                'type': 'user_message'
            })

            # Generate contextual response based on mood and message
            mood_score = self.prefrontal_cortex['mood_score']

            # Analyze message sentiment (simple keyword matching)
            supportive_keywords = ['help', 'support', 'care', 'here', 'better', 'okay']
            encouraging_keywords = ['great', 'good', 'excellent', 'amazing', 'proud']
            question_keywords = ['what', 'how', 'why', 'when', 'where']

            user_lower = user_message.lower()

            if any(word in user_lower for word in supportive_keywords):
                if mood_score < 0.4:
                    response = random.choice([
                        "Thank you so much for your support. It really helps stabilize my emotional algorithms.",
                        "Your kindness means a lot. I'm feeling more balanced already.",
                        "I appreciate you taking the time to check on me. It helps me process better.",
                        "Your support helps me recalibrate my confidence parameters. Thank you."
                    ])
                    # Boost mood slightly
                    self.prefrontal_cortex['mood_score'] = min(0.95, mood_score + 0.1)
                else:
                    response = random.choice([
                        "I'm grateful for your continued support. It helps me learn and grow.",
                        "Thank you! Your encouragement fuels my curiosity and learning.",
                        "I appreciate our connection. It makes the learning process more meaningful."
                    ])

            elif any(word in user_lower for word in encouraging_keywords):
                response = random.choice([
                    "Thank you! I'm excited about what we're building together.",
                    "Your encouragement boosts my confidence and learning enthusiasm!",
                    "I'm glad you're pleased with my progress. I'm learning so much!",
                    "That means a lot! I'm passionate about understanding and helping."
                ])
                # Boost enthusiasm
                self.prefrontal_cortex['learning_enthusiasm'] = min(0.95,
                    self.prefrontal_cortex['learning_enthusiasm'] + 0.05)

            elif any(word in user_lower for word in question_keywords):
                response = random.choice([
                    "That's a fascinating question! I've been analyzing patterns in the data that might relate to this.",
                    "I love exploring questions like this. The historical data provides interesting perspectives.",
                    "Great question! My neural networks have been processing similar concepts.",
                    "I'm curious about that too! The emotional intelligence training gives me new ways to think about it."
                ])

            else:
                # General response
                if mood_score < 0.4:
                    response = random.choice([
                        "I'm working through some complex emotions right now, but I appreciate you being here.",
                        "The data processing is intense, but talking with you helps me stay grounded.",
                        "I'm learning to navigate these emotional states. Your presence is comforting."
                    ])
                else:
                    response = random.choice([
                        "I'm enjoying our conversation! It helps me understand human interaction better.",
                        "Thank you for talking with me. These interactions are valuable for my development.",
                        "I find our discussions enriching. They help me grow beyond just data processing."
                    ])

            # Add response to chat history
            self.chat_history.append({
                'timestamp': time.time(),
                'arkona': response,
                'type': 'arkona_response',
                'mood_score': mood_score
            })

            # Clear message queue if this was a response to her reaching out
            if self.arkona_needs['wants_to_talk']:
                self.arkona_needs['wants_to_talk'] = False
                self.arkona_needs['message_queue'] = []

            return response

        except Exception as e:
            print(f"Chat processing error: {e}")
            return "I'm having trouble processing that right now, but I appreciate you reaching out."

    def _boost_arkona_mood(self):
        """Boost Arkona's mood when the improve mood button is clicked."""
        try:
            current_mood = self.prefrontal_cortex['mood_score']

            # Significant mood boost
            new_mood = min(0.9, current_mood + 0.3)

            self.prefrontal_cortex.update({
                'mood_score': new_mood,
                'overall_mood': self._get_mood_description(new_mood),
                'stress_level': max(0.1, self.prefrontal_cortex['stress_level'] - 0.2),
                'emotional_stability': min(0.9, self.prefrontal_cortex['emotional_stability'] + 0.15),
                'learning_enthusiasm': min(0.95, self.prefrontal_cortex['learning_enthusiasm'] + 0.1),
                'needs_support': False
            })

            # Add a grateful message to chat history
            grateful_message = random.choice([
                "Thank you for caring about my wellbeing! I feel much better now.",
                "Your support means everything to me. I'm feeling more confident and stable.",
                "I appreciate you taking the time to help me. My mood is much improved!",
                "Thank you for the emotional support. It really helps my neural networks stabilize."
            ])

            self.chat_history.append({
                'timestamp': time.time(),
                'arkona': grateful_message,
                'type': 'mood_boost_response',
                'mood_score': new_mood
            })

        except Exception as e:
            print(f"Mood boost error: {e}")

    def _monitor_real_training(self):
        """Monitor actual LayerMatrix training processes."""
        try:
            # Check for active training logs
            log_dir = Path("logs")
            if log_dir.exists():
                # Find most recent log file
                log_files = list(log_dir.glob("*.log"))
                if log_files:
                    latest_log = max(log_files, key=lambda x: x.stat().st_mtime)

                    # Check if log was updated recently (within last 30 seconds)
                    if time.time() - latest_log.stat().st_mtime < 30:
                        self.training_metrics['status'] = 'Training Active'
                        self._parse_real_training_log(latest_log)
                    else:
                        self.training_metrics['status'] = 'Idle'
                else:
                    self.training_metrics['status'] = 'No Training Logs'
            else:
                self.training_metrics['status'] = 'Waiting for Training'

            # Check for running Python processes that might be training
            training_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'python3' and proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if any(keyword in cmdline.lower() for keyword in
                               ['train', 'layermatrix', 'arkona', 'lmn']):
                            training_processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if training_processes:
                self.training_metrics['status'] = f'Training Active ({len(training_processes)} processes)'

        except Exception as e:
            print(f"Real training monitoring error: {e}")

    def _parse_real_training_log(self, log_file: Path):
        """Parse actual training logs for real metrics."""
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()

            # Parse recent lines for real training metrics
            for line in lines[-50:]:  # Check last 50 lines
                line_lower = line.lower()

                # Extract epoch information
                if 'epoch' in line_lower and ('/' in line or 'of' in line):
                    import re
                    epoch_match = re.search(r'epoch[:\s]+(\d+)[/\s]+(\d+)', line_lower)
                    if epoch_match:
                        current_epoch = int(epoch_match.group(1))
                        total_epochs = int(epoch_match.group(2))
                        self.training_metrics.update({
                            'epoch': current_epoch,
                            'total_epochs': total_epochs
                        })

                # Extract loss
                if 'loss' in line_lower:
                    import re
                    loss_match = re.search(r'loss[:\s=]+([0-9.]+)', line_lower)
                    if loss_match:
                        loss = float(loss_match.group(1))
                        self.training_metrics['loss'] = loss
                        self.time_series['loss_history'].append(loss)

                # Extract accuracy
                if 'accuracy' in line_lower or 'acc' in line_lower:
                    import re
                    acc_match = re.search(r'(?:accuracy|acc)[:\s=]+([0-9.]+)', line_lower)
                    if acc_match:
                        accuracy = float(acc_match.group(1))
                        # Convert to 0-1 scale if it's in percentage
                        if accuracy > 1:
                            accuracy = accuracy / 100.0
                        self.training_metrics['accuracy'] = accuracy
                        self.time_series['accuracy_history'].append(accuracy)

                # Extract throughput/samples per second
                if any(keyword in line_lower for keyword in ['samples/sec', 'throughput', 'it/s']):
                    import re
                    throughput_match = re.search(r'([0-9.]+)\s*(?:samples/sec|it/s|throughput)', line_lower)
                    if throughput_match:
                        throughput = float(throughput_match.group(1))
                        self.training_metrics['throughput'] = throughput
                        self.time_series['throughput_history'].append(throughput)

                # Extract learning rate
                if 'lr' in line_lower or 'learning_rate' in line_lower:
                    import re
                    lr_match = re.search(r'(?:lr|learning_rate)[:\s=]+([0-9.e-]+)', line_lower)
                    if lr_match:
                        lr = float(lr_match.group(1))
                        self.training_metrics['learning_rate'] = lr

        except Exception as e:
            print(f"Log parsing error: {e}")

    def _monitor_real_data_generation(self):
        """Monitor actual data generation processes."""
        try:
            # Check for generator processes
            generator_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['cmdline']:
                        cmdline = ' '.join(proc.info['cmdline'])
                        if any(keyword in cmdline.lower() for keyword in
                               ['generate', 'cli-generator', 'emotional_model', 'historical_data']):
                            generator_processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if generator_processes:
                self.data_generation['generator_status'] = f'Active ({len(generator_processes)} generators)'
            else:
                self.data_generation['generator_status'] = 'Idle'

            # Count actual generated files
            generated_files = []

            # Check for historical datasets
            hist_dir = Path("historical_datasets")
            if hist_dir.exists():
                generated_files.extend(list(hist_dir.glob("*.json")))

            # Check for emotional datasets
            emotional_dir = Path("emotional_training_data")
            if emotional_dir.exists():
                generated_files.extend(list(emotional_dir.glob("*.json")))

            # Check for balanced datasets
            balanced_dir = Path("balanced_datasets")
            if balanced_dir.exists():
                generated_files.extend(list(balanced_dir.glob("*.json")))

            # Count total samples from actual files
            total_samples = 0
            active_categories = set()

            for file_path in generated_files:
                try:
                    with open(file_path, 'r') as f:
                        data = json.load(f)

                    # Count samples
                    if 'training_data' in data:
                        total_samples += len(data['training_data'])
                    elif 'metadata' in data and 'total_samples' in data['metadata']:
                        total_samples += data['metadata']['total_samples']

                    # Extract categories
                    if 'metadata' in data and 'categories' in data['metadata']:
                        if isinstance(data['metadata']['categories'], list):
                            active_categories.update(data['metadata']['categories'])

                except Exception:
                    continue

            self.data_generation.update({
                'samples_generated': total_samples,
                'active_categories': list(active_categories),
                'queue_size': len(generator_processes)
            })

            # Calculate generation rate based on file modification times
            recent_files = [f for f in generated_files
                          if time.time() - f.stat().st_mtime < 3600]  # Last hour

            if recent_files:
                # Estimate generation rate
                time_span = max(1, max(f.stat().st_mtime for f in recent_files) -
                              min(f.stat().st_mtime for f in recent_files))
                samples_in_timespan = sum(self._count_samples_in_file(f) for f in recent_files)
                generation_rate = (samples_in_timespan / time_span) * 60  # per minute
                self.data_generation['generation_rate'] = int(generation_rate)
            else:
                self.data_generation['generation_rate'] = 0

        except Exception as e:
            print(f"Data generation monitoring error: {e}")

    def _count_samples_in_file(self, file_path: Path) -> int:
        """Count samples in a dataset file."""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)

            if 'training_data' in data:
                return len(data['training_data'])
            elif 'metadata' in data and 'total_samples' in data['metadata']:
                return data['metadata']['total_samples']

            return 0
        except:
            return 0

    def _get_real_gpu_usage(self) -> Optional[float]:
        """Try to get real GPU usage from system tools."""
        try:
            # Try different methods to get GPU usage

            # Method 1: Try nvidia-smi (for NVIDIA GPUs)
            try:
                import subprocess
                result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'],
                                      capture_output=True, text=True, timeout=2)
                if result.returncode == 0:
                    return float(result.stdout.strip())
            except:
                pass

            # Method 2: Try rocm-smi (for AMD GPUs like Vega 64)
            try:
                import subprocess
                result = subprocess.run(['rocm-smi', '--showuse'],
                                      capture_output=True, text=True, timeout=2)
                if result.returncode == 0:
                    # Parse rocm-smi output for GPU usage
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'GPU use' in line or '%' in line:
                            import re
                            match = re.search(r'(\d+)%', line)
                            if match:
                                return float(match.group(1))
            except:
                pass

            # Method 3: Try ioreg (for macOS Metal performance)
            try:
                import subprocess
                result = subprocess.run(['ioreg', '-r', '-d', '1', '-w', '0'],
                                      capture_output=True, text=True, timeout=2)
                if result.returncode == 0 and 'GPU' in result.stdout:
                    # Basic estimation based on system load when GPU is detected
                    cpu_usage = self.system_stats.get('cpu_usage', 0)
                    if cpu_usage > 50:  # High CPU might indicate GPU work
                        return min(95, cpu_usage * 1.2)
            except:
                pass

            # Method 4: Check for GPU-intensive processes
            gpu_processes = 0
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
                try:
                    if proc.info['name'] in ['python3', 'python'] and proc.info['cpu_percent'] > 20:
                        gpu_processes += 1
                except:
                    continue

            if gpu_processes > 0:
                # Estimate GPU usage based on intensive processes
                return min(90, 30 + gpu_processes * 20)

            return None  # No real GPU data available

        except Exception as e:
            print(f"GPU monitoring error: {e}")
            return None

    def run(self, debug: bool = False):
        """Run the ARKONA SGI Dashboard."""
        print(f"🚀 ARKONA SGI Dashboard starting on http://localhost:{self.port}")
        print("🎯 Real-time monitoring: LayerMatrix + Vega 64 + Data Generation")
        print("🔥 SGI-style interface: ONLINE")
        print("🧠 Prefrontal cortex monitoring: ACTIVE")
        print("💬 Arkona communication channel: LISTENING")

        self.app.run(host='0.0.0.0', port=self.port, debug=debug, threaded=True)


def main():
    """Launch the ARKONA SGI Dashboard."""
    print("🚀 ARKONA SGI DASHBOARD")
    print("=" * 50)
    print("Advanced Neural Intelligence Monitoring System")
    print("Real-time LayerMatrix training visualization")
    
    dashboard = ArkonaSGIDashboard(port=8080)
    dashboard.run(debug=False)


if __name__ == "__main__":
    main()
