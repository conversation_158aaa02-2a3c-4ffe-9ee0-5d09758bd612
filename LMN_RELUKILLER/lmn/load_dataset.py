import json
from typing import Any, Dict
import pandas as pd
import logging
import datasets
import datasets.domain

logger = logging.getLogger(__name__)

def load_dataset(file_path):
    """
    Load a dataset from a JSON file.
    
    Args:
        file_path (str): Path to the JSON dataset file.
        
    Returns:
        dict: Parsed dataset content.
    """
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"Error loading dataset from {file_path}: {e}")
        return None
    
def format_dataset(data):
    """
    Format the dataset into a structure suitable for training.
    
    Args:
        data (dict): The raw dataset content.
        
    Returns:
        list: A list of formatted examples.
    """
    formatted_data = []
    
    for item in data.get('examples', []):
        formatted_example = {
            'input': item.get('input', ''),
            'output': item.get('output', '')
        }
        formatted_data.append(formatted_example)
    
    return formatted_data

def load_training_data(self) -> None:
        """Load and preprocess ARKONA training dataset"""
        if not self.dataset_path:
            raise ValueError("Dataset path not provided")
            
        logger.info(f"Loading training data from: {self.dataset_path}")
        
        with open(self.dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        emotion_examples = data.get('examples', {}).get('emotion_context', [])
        ethical_examples = data.get('examples', {}).get('ethical_reasoning', [])
        curiosity_examples = data.get('examples', {}).get('curiosity_driven', [])
        
        logger.info(f"Loaded {len(emotion_examples)} emotion examples")
        logger.info(f"Loaded {len(ethical_examples)} ethical examples") 
        logger.info(f"Loaded {len(curiosity_examples)} curiosity examples")
        
        training_data = []
        
        # Process examples
        for example in emotion_examples:
            training_data.append(self._format_emotion_example(example))
        
        for example in ethical_examples:
            training_data.append(self._format_ethical_example(example))
            
        for example in curiosity_examples:
            training_data.append(self._format_curiosity_example(example))
        
        df = pd.DataFrame(training_data)
        df['text'] = df['text'].apply(lambda x: x.replace('<|endoftext|>', ''))

        logger.info(f"Created dataset with {len(training_data)} total examples")
            
def _format_emotion_example(self, example: Dict[str, Any]) -> Dict[str, str]:
        """Format emotion context example"""
        context = example.get('context_input', {})
        responses = example.get('responses', {})
        
        emotion_state = example.get('emotional_state', {}).get('label', 'neutral')
        query = context.get('query', '')
        persona = context.get('persona', 'User')
        
        # Simple conversational format for DialoGPT
        input_text = f"[{emotion_state} {persona}] {query}"
        target_text = responses.get('emotion_adapted_response', responses.get('standard_response', ''))
        
        # DialoGPT format
        conversation = f"{input_text}<|endoftext|>{target_text}<|endoftext|>"
        
        return {
            'text': conversation,
            'category': 'emotion'
        }
    
def _format_ethical_example(self, example: Dict[str, Any]) -> Dict[str, str]:
        """Format ethical reasoning example"""
        scenario = example.get('scenario', '')
        analysis = example.get('analysis', {})
        
        input_text = f"[Ethical] {scenario}"
        decision = analysis.get('decision', '')
        rationale = analysis.get('rationale', '')
        target_text = f"{decision} {rationale}"
        
        conversation = f"{input_text}<|endoftext|>{target_text}<|endoftext|>"
        
        return {
            'text': conversation,
            'category': 'ethical'
        }
    

def _format_curiosity_example(self, example: Dict[str, Any]) -> Dict[str, str]:
        """Format curiosity-driven example"""
        context = example.get('context', '')
        strategy = example.get('investigation_strategy', {})
        
        input_text = f"[Research] {context}"
        approach = strategy.get('approach', '')
        target_text = f"Approach: {approach}"
        
        conversation = f"{input_text}<|endoftext|>{target_text}<|endoftext|>"
        
        return {
            'text': conversation,
            'category': 'curiosity'
        }