#!/usr/bin/env python3
"""
Train LayerMatrix on Real-World Arkona Data

This script trains the enhanced LayerMatrix on the rich Arkona dataset
to achieve high accuracy on real-world tasks including:
- Domain classification (psychology, AI, finance, etc.)
- Emotion recognition and adaptation
- Knowledge extraction and reasoning
- Confidence prediction
"""

import sys
import os
import numpy as np
import time
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, accuracy_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# Import our modules
sys.path.insert(0, os.path.dirname(__file__))

import importlib.util
spec = importlib.util.spec_from_file_location("MassiveLayerMatrix", "lmn/MassiveLayerMatrix.py")
massive_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(massive_module)
MassiveLayerMatrix = massive_module.MassiveLayerMatrix

from arkona_data_processor import ArkonaDataProcessor

class ArkonaLayerMatrixTrainer:
    """Train LayerMatrix on Arkona data with accuracy optimizations."""
    
    def __init__(self):
        self.processor = ArkonaDataProcessor()
        self.models = {}
        self.scalers = {}
        self.results = {}
        
        print("🎯 Arkona LayerMatrix Trainer initialized!")
    
    def load_or_process_data(self):
        """Load processed data or create it if it doesn't exist."""
        base_path = self.processor.base_path
        
        # Try to load existing processed data
        class_file = os.path.join(base_path, "arkona_classification_data.npz")
        reg_file = os.path.join(base_path, "arkona_regression_data.npz")
        
        if os.path.exists(class_file):
            print("📂 Loading existing classification data...")
            data = np.load(class_file)
            X_class, y_class = data['X'], data['y']
        else:
            print("🔄 Processing classification data...")
            X_class, y_class, class_names = self.processor.create_classification_dataset()
            if len(X_class) > 0:
                self.processor.save_processed_data(X_class, y_class, "arkona_classification_data.npz")
        
        if os.path.exists(reg_file):
            print("📂 Loading existing regression data...")
            data = np.load(reg_file)
            X_reg, y_reg = data['X'], data['y']
        else:
            print("🔄 Processing regression data...")
            X_reg, y_reg = self.processor.create_regression_dataset()
            if len(X_reg) > 0:
                self.processor.save_processed_data(X_reg, y_reg, "arkona_regression_data.npz")
        
        return (X_class, y_class) if len(X_class) > 0 else (None, None), \
               (X_reg, y_reg) if len(X_reg) > 0 else (None, None)
    
    def train_classification_model(self, X: np.ndarray, y: np.ndarray):
        """Train LayerMatrix for domain/emotion classification."""
        print("\n🧠 TRAINING CLASSIFICATION MODEL")
        print("=" * 50)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"Training set: {X_train.shape[0]:,} samples")
        print(f"Test set: {X_test.shape[0]:,} samples")
        print(f"Features: {X_train.shape[1]:,}")
        print(f"Classes: {len(np.unique(y))}")
        
        # Create optimized LayerMatrix for classification
        model = MassiveLayerMatrix(
            hidden_layers=[1024, 512, 256],  # Optimized architecture
            accumulation_ratio=0.15,         # Reduced for stability
            learning_rate=1e-4,              # Conservative learning rate
            micro_batch_size=16,             # Larger batches for stability
            gradient_accumulation_steps=32,   # Effective batch size: 512
            max_memory_gb=8.0,               # Full Vega 64 memory
            use_mixed_precision=True,
            checkpoint_layers=True,
            offload_to_cpu=False,
            # Accuracy-focused parameters
            adaptive_accumulation=True,
            layer_norm=True,
            dropout_rate=0.2,               # Regularization
            weight_decay=1e-4,              # L2 regularization
            warmup_steps=200
        )
        
        # Train with cross-validation for robustness
        print("\n🔄 Training with cross-validation...")
        cv_scores = []
        
        kf = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
        for fold, (train_idx, val_idx) in enumerate(kf.split(X_train_scaled, y_train)):
            print(f"\n   Fold {fold + 1}/3...")
            
            X_fold_train, X_fold_val = X_train_scaled[train_idx], X_train_scaled[val_idx]
            y_fold_train, y_fold_val = y_train[train_idx], y_train[val_idx]
            
            # Create fresh model for each fold
            fold_model = MassiveLayerMatrix(
                hidden_layers=[1024, 512, 256],
                accumulation_ratio=0.15,
                learning_rate=1e-4,
                micro_batch_size=16,
                gradient_accumulation_steps=32,
                max_memory_gb=8.0,
                use_mixed_precision=True,
                adaptive_accumulation=True,
                layer_norm=True,
                dropout_rate=0.2,
                weight_decay=1e-4,
                warmup_steps=200
            )
            
            # Train
            fold_model.fit(X_fold_train, y_fold_train, epochs=10, verbose=False)
            
            # Evaluate
            val_predictions = fold_model.predict(X_fold_val)
            val_accuracy = accuracy_score(y_fold_val, val_predictions)
            cv_scores.append(val_accuracy)
            
            print(f"      Fold {fold + 1} accuracy: {val_accuracy:.3f}")
            
            fold_model.cleanup()
        
        print(f"\n   Cross-validation accuracy: {np.mean(cv_scores):.3f} ± {np.std(cv_scores):.3f}")
        
        # Train final model on full training set
        print("\n🎯 Training final model...")
        start_time = time.time()
        
        model.fit(X_train_scaled, y_train, epochs=15, verbose=True)
        
        train_time = time.time() - start_time
        
        # Evaluate on test set
        print("\n📊 Evaluating on test set...")
        test_predictions = model.predict(X_test_scaled)
        test_accuracy = accuracy_score(y_test, test_predictions)
        
        print(f"\n🏆 CLASSIFICATION RESULTS:")
        print(f"   Cross-validation: {np.mean(cv_scores):.3f} ± {np.std(cv_scores):.3f}")
        print(f"   Test accuracy: {test_accuracy:.3f}")
        print(f"   Training time: {train_time:.2f}s")
        print(f"   Model parameters: {model._estimate_parameters():,}")
        
        # Store results
        self.models['classification'] = model
        self.scalers['classification'] = scaler
        self.results['classification'] = {
            'cv_accuracy': np.mean(cv_scores),
            'cv_std': np.std(cv_scores),
            'test_accuracy': test_accuracy,
            'train_time': train_time,
            'parameters': model._estimate_parameters()
        }
        
        return model, test_accuracy
    
    def train_regression_model(self, X: np.ndarray, y: np.ndarray):
        """Train LayerMatrix for confidence/score regression."""
        print("\n📈 TRAINING REGRESSION MODEL")
        print("=" * 50)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Scale features and targets
        scaler_X = StandardScaler()
        scaler_y = StandardScaler()
        
        X_train_scaled = scaler_X.fit_transform(X_train)
        X_test_scaled = scaler_X.transform(X_test)
        
        y_train_scaled = scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
        y_test_scaled = scaler_y.transform(y_test.reshape(-1, 1)).flatten()
        
        print(f"Training set: {X_train.shape[0]:,} samples")
        print(f"Test set: {X_test.shape[0]:,} samples")
        print(f"Target range: {y.min():.3f} - {y.max():.3f}")
        
        # Create regression model
        model = MassiveLayerMatrix(
            hidden_layers=[512, 256, 128],   # Smaller for regression
            accumulation_ratio=0.2,
            learning_rate=5e-4,              # Slightly higher for regression
            micro_batch_size=32,
            gradient_accumulation_steps=16,
            max_memory_gb=8.0,
            use_mixed_precision=True,
            adaptive_accumulation=True,
            layer_norm=True,
            dropout_rate=0.1,               # Less dropout for regression
            weight_decay=1e-5,
            warmup_steps=100
        )
        
        # Train
        print("\n🎯 Training regression model...")
        start_time = time.time()
        
        model.fit(X_train_scaled, y_train_scaled, epochs=20, verbose=True)
        
        train_time = time.time() - start_time
        
        # Evaluate
        print("\n📊 Evaluating regression model...")
        test_predictions_scaled = model.predict(X_test_scaled)
        
        # Convert back to original scale
        test_predictions = scaler_y.inverse_transform(test_predictions_scaled.reshape(-1, 1)).flatten()
        
        mse = mean_squared_error(y_test, test_predictions)
        rmse = np.sqrt(mse)
        mae = np.mean(np.abs(y_test - test_predictions))
        
        print(f"\n🏆 REGRESSION RESULTS:")
        print(f"   RMSE: {rmse:.4f}")
        print(f"   MAE: {mae:.4f}")
        print(f"   Training time: {train_time:.2f}s")
        print(f"   Model parameters: {model._estimate_parameters():,}")
        
        # Store results
        self.models['regression'] = model
        self.scalers['regression'] = (scaler_X, scaler_y)
        self.results['regression'] = {
            'rmse': rmse,
            'mae': mae,
            'train_time': train_time,
            'parameters': model._estimate_parameters()
        }
        
        return model, rmse
    
    def run_comprehensive_training(self):
        """Run complete training pipeline on Arkona data."""
        print("🚀 COMPREHENSIVE ARKONA LAYERMATRIX TRAINING")
        print("=" * 70)
        
        # Load data
        (X_class, y_class), (X_reg, y_reg) = self.load_or_process_data()
        
        results_summary = {}
        
        # Train classification model
        if X_class is not None:
            class_model, class_acc = self.train_classification_model(X_class, y_class)
            results_summary['classification'] = class_acc
        else:
            print("⚠️  No classification data available")
        
        # Train regression model
        if X_reg is not None:
            reg_model, reg_rmse = self.train_regression_model(X_reg, y_reg)
            results_summary['regression'] = reg_rmse
        else:
            print("⚠️  No regression data available")
        
        # Final summary
        print(f"\n🎉 TRAINING COMPLETE!")
        print("=" * 70)
        
        for task, result in results_summary.items():
            if task == 'classification':
                print(f"✅ Classification accuracy: {result:.3f}")
            elif task == 'regression':
                print(f"✅ Regression RMSE: {result:.4f}")
        
        print(f"\n🏆 LayerMatrix successfully trained on real-world Arkona data!")
        print(f"   Ready for deployment on complex NLP tasks")
        
        return results_summary


def main():
    """Main training function."""
    trainer = ArkonaLayerMatrixTrainer()
    results = trainer.run_comprehensive_training()
    
    print(f"\n📊 Final Results Summary:")
    for task, metric in results.items():
        print(f"   {task}: {metric}")


if __name__ == "__main__":
    main()
