import React, { useState, useCallback, useEffect } from 'react';
import type { QAPair, GenerationConfig } from './types';
import { generateQaPairs } from './services/geminiService';
import Header from './components/Header';
import ContextEditor from './components/ContextEditor';
import QaList from './components/QaList';
import Button from './components/Button';
import { FileDown, PlusCircle, History } from 'lucide-react';

const LOCAL_STORAGE_KEY = 'qa-generator-session-v2';

const RestoreSessionPrompt = ({ onRestore, onDismiss }: { onRestore: () => void, onDismiss: () => void }) => (
  <div className="fixed top-0 left-0 right-0 bg-slate-800 border-b border-cyan-500 shadow-lg z-20 p-4 flex items-center justify-center gap-4 animate-fade-in-down">
    <History className="w-6 h-6 text-cyan-400" />
    <p className="text-slate-200">
      <span className="font-semibold">We found a saved session.</span> Would you like to restore it?
    </p>
    <Button onClick={onRestore} variant="primary" className="py-1 px-3">
      Restore
    </Button>
    <Button onClick={onDismiss} variant="secondary" className="py-1 px-3">
      Dismiss
    </Button>
  </div>
);

export default function App() {
  const [context, setContext] = useState<string>('');
  const [largeFiles, setLargeFiles] = useState<File[] | null>(null);
  const [qaPairs, setQaPairs] = useState<QAPair[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [generationProgress, setGenerationProgress] = useState<number>(0);
  const [restoreData, setRestoreData] = useState<{ context: string; qaPairs: QAPair[], largeFileNames?: string[] } | null>(null);
  const [restoredFileNames, setRestoredFileNames] = useState<string[] | null>(null);

  const [generationConfig, setGenerationConfig] = useState<GenerationConfig>({
    samplesToGenerate: 1000,
    sampleType: 'Fact-based Q&A',
    persona: 'Neutral',
    tone: 'Neutral',
    additionalInstructions: '',
  });

  useEffect(() => {
    try {
      const savedData = localStorage.getItem(LOCAL_STORAGE_KEY);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        if (parsedData.context || (parsedData.qaPairs?.length > 0) || (parsedData.largeFileNames?.length > 0)) {
          setRestoreData(parsedData);
        }
      }
    } catch (e) {
      console.error("Failed to load saved session from localStorage", e);
      localStorage.removeItem(LOCAL_STORAGE_KEY);
    }
  }, []);

  useEffect(() => {
    const handler = setTimeout(() => {
      if (!restoreData) {
        try {
          const dataToSave = JSON.stringify({ 
            context, 
            qaPairs,
            largeFileNames: largeFiles ? largeFiles.map(f => f.name) : null
          });
          localStorage.setItem(LOCAL_STORAGE_KEY, dataToSave);
        } catch (e) {
          console.error("Failed to save session to localStorage", e);
        }
      }
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [context, qaPairs, largeFiles, restoreData]);

  const handleRestore = () => {
    if (restoreData) {
      setQaPairs(restoreData.qaPairs || []);
      if (restoreData.largeFileNames && restoreData.largeFileNames.length > 0) {
        setContext('');
        setLargeFiles(null);
        setRestoredFileNames(restoreData.largeFileNames);
      } else {
        setContext(restoreData.context || '');
        setLargeFiles(null);
        setRestoredFileNames(null);
      }
    }
    setRestoreData(null);
  };

  const handleDismiss = () => {
    localStorage.removeItem(LOCAL_STORAGE_KEY);
    setRestoreData(null);
  };

  const handleGenerateFromText = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    setGenerationProgress(0);

    const totalSamples = generationConfig.samplesToGenerate;
    const SAMPLES_PER_BATCH = 10;
    const totalBatches = Math.ceil(totalSamples / SAMPLES_PER_BATCH);
    
    try {
      let currentPairs = [...qaPairs];
      for (let i = 0; i < totalBatches; i++) {
        if (currentPairs.length >= totalSamples) break;

        const recentQuestions = currentPairs.slice(-50).map(p => p.question);
        const newPairsBatch = await generateQaPairs(context, recentQuestions, generationConfig);

        const existingQuestionSet = new Set(currentPairs.map(p => p.question.toLowerCase()));
        const uniqueNewPairs = newPairsBatch.filter(p => !existingQuestionSet.has(p.question.toLowerCase()));
        
        if (uniqueNewPairs.length > 0) {
            let combined = [...currentPairs, ...uniqueNewPairs];
            if (combined.length > totalSamples) {
              combined = combined.slice(0, totalSamples);
            }
            currentPairs = combined;
            setQaPairs(currentPairs);
        }

        setGenerationProgress(Math.round(((currentPairs.length) / totalSamples) * 100));

        if (i > 10 && newPairsBatch.length < 3) {
            setError("Generation stopped early. The model may be struggling to find more unique Q&A pairs. Try a different context.");
            break; 
        }
      }
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
      setError(`Generation failed: ${errorMessage}`);
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  }, [context, qaPairs, generationConfig]);

  const handleGenerateFromFiles = useCallback(async () => {
    if (!largeFiles) return;

    setIsLoading(true);
    setError(null);
    setGenerationProgress(0);

    const CHUNK_SIZE = 500000;
    const totalSamples = generationConfig.samplesToGenerate;
    let currentPairs = [...qaPairs];

    try {
        const totalSize = largeFiles.reduce((acc, f) => acc + f.size, 0);
        let bytesProcessed = 0;
        let generationComplete = false;

        for (const file of largeFiles) {
            if (generationComplete) break;
            const stream = file.stream();
            const reader = stream.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (!generationComplete) {
                const { done, value } = await reader.read();
                if (done) {
                    if (buffer.length > 0) {
                        const recentQuestions = currentPairs.slice(-50).map(p => p.question);
                        const newPairs = await generateQaPairs(buffer, recentQuestions, generationConfig);
                        currentPairs = [...currentPairs, ...newPairs].slice(0, totalSamples);
                        setQaPairs(currentPairs);
                    }
                    if (currentPairs.length >= totalSamples) generationComplete = true;
                    break;
                }

                buffer += decoder.decode(value, { stream: true });

                while (buffer.length >= CHUNK_SIZE) {
                    if (currentPairs.length >= totalSamples) {
                        generationComplete = true;
                        break;
                    }
                    const chunk = buffer.substring(0, CHUNK_SIZE);
                    buffer = buffer.substring(CHUNK_SIZE);
                    
                    const recentQuestions = currentPairs.slice(-50).map(p => p.question);
                    const newPairs = await generateQaPairs(chunk, recentQuestions, generationConfig);
                    
                    currentPairs = [...currentPairs, ...newPairs];
                    if (currentPairs.length >= totalSamples) {
                        currentPairs = currentPairs.slice(0, totalSamples);
                        generationComplete = true;
                    }
                    setQaPairs(currentPairs);
                    
                    bytesProcessed += new TextEncoder().encode(chunk).byteLength;
                    setGenerationProgress(Math.min(100, Math.round((bytesProcessed / totalSize) * 100)));
                }
            }
        }
        setGenerationProgress(100);
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
      setError(`Generation failed during file processing: ${errorMessage}`);
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  }, [largeFiles, qaPairs, generationConfig]);

  const handleGenerate = useCallback(() => {
    setError(null);
    if (largeFiles && largeFiles.length > 0) {
      handleGenerateFromFiles();
    } else if (context.trim()) {
      handleGenerateFromText();
    } else {
      setError('No context provided. Please paste text or upload a file.');
    }
  }, [context, largeFiles, handleGenerateFromText, handleGenerateFromFiles]);

  const handleUpdatePair = useCallback((id: string, updatedPair: Partial<Omit<QAPair, 'id'>>) => {
    setQaPairs(prevPairs =>
      prevPairs.map(pair => (pair.id === id ? { ...pair, ...updatedPair } : pair))
    );
  }, []);

  const handleDeletePair = useCallback((id: string) => {
    setQaPairs(prevPairs => prevPairs.filter(pair => pair.id !== id));
  }, []);

  const handleAddPair = useCallback(() => {
    const newPair: QAPair = {
      id: `manual-${Date.now()}`,
      question: '',
      answer: '',
    };
    setQaPairs(prevPairs => [newPair, ...prevPairs]);
  }, []);

  const handleExport = useCallback(() => {
    if (qaPairs.length === 0) {
        setError('There is no data to export.');
        return;
    }
    setError(null);
    const dataToExport = qaPairs.map(({ question, answer }) => ({ question, answer }));
    const jsonString = JSON.stringify(dataToExport, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'qa_training_data.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [qaPairs]);

  return (
    <div className="min-h-screen bg-slate-900 text-slate-100 font-sans">
      {restoreData && <RestoreSessionPrompt onRestore={handleRestore} onDismiss={handleDismiss} />}
      <Header />
      <main className="container mx-auto p-4 md:p-6 lg:p-8 grid grid-cols-1 lg:grid-cols-2 gap-8 mt-4">
        <ContextEditor
          context={context}
          setContext={setContext}
          largeFiles={largeFiles}
          setLargeFiles={setLargeFiles}
          onGenerate={handleGenerate}
          isLoading={isLoading}
          generationProgress={generationProgress}
          restoredFileNames={restoredFileNames}
          setRestoredFileNames={setRestoredFileNames}
          generationConfig={generationConfig}
          setGenerationConfig={setGenerationConfig}
        />
        
        <div className="flex flex-col h-full bg-slate-800/50 rounded-xl border border-slate-700 shadow-2xl">
          <div className="flex justify-between items-center p-4 border-b border-slate-700">
            <h2 className="text-xl font-bold text-white">Generated Q&A Pairs ({qaPairs.length})</h2>
            <div className="flex items-center gap-2">
              <Button onClick={handleAddPair} disabled={isLoading} variant="secondary">
                <PlusCircle className="w-4 h-4 mr-2" />
                Add Pair
              </Button>
              <Button onClick={handleExport} disabled={qaPairs.length === 0 || isLoading}>
                <FileDown className="w-4 h-4 mr-2" />
                Export JSON
              </Button>
            </div>
          </div>

          {error && (
            <div className="m-4 p-3 bg-red-900/50 border border-red-700 text-red-200 rounded-lg">
              <p className="font-semibold">Error</p>
              <p>{error}</p>
            </div>
          )}

          <QaList
            pairs={qaPairs}
            onUpdate={handleUpdatePair}
            onDelete={handleDeletePair}
          />
        </div>
      </main>
    </div>
  );
}
