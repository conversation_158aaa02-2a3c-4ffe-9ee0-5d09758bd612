import React from 'react';
import type { QAPair } from '../types';
import QaCard from './QaCard';
import { Info } from 'lucide-react';

interface QaListProps {
  pairs: QAPair[];
  onUpdate: (id: string, updatedPair: Partial<Omit<QAPair, 'id'>>) => void;
  onDelete: (id: string) => void;
}

const QaList: React.FC<QaListProps> = ({ pairs, onUpdate, onDelete }) => {
  if (pairs.length === 0) {
    return (
      <div className="flex-grow flex flex-col items-center justify-center p-8 text-center text-slate-500">
        <Info className="w-12 h-12 mb-4" />
        <h3 className="text-lg font-semibold text-slate-400">No Q&A Pairs Yet</h3>
        <p className="max-w-xs">
          Generate pairs from your context, or add one manually to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="flex-grow overflow-y-auto p-4 space-y-4">
      {pairs.map(pair => (
        <QaCard
          key={pair.id}
          pair={pair}
          onUpdate={onUpdate}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
};

export default QaList;
