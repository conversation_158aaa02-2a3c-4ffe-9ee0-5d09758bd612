import React from 'react';
import type { QAPair } from '../types';
import { Trash2 } from 'lucide-react';

interface QaCardProps {
  pair: QAPair;
  onUpdate: (id: string, updatedPair: Partial<Omit<QAPair, 'id'>>) => void;
  onDelete: (id: string) => void;
}

const QaCard: React.FC<QaCardProps> = ({ pair, onUpdate, onDelete }) => {
  const handleQuestionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onUpdate(pair.id, { question: e.target.value });
  };

  const handleAnswerChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onUpdate(pair.id, { answer: e.target.value });
  };

  return (
    <div className="bg-slate-900 border border-slate-700 rounded-lg p-4 transition-shadow hover:shadow-lg hover:border-slate-600 relative group">
       <button 
        onClick={() => onDelete(pair.id)}
        className="absolute top-2 right-2 p-1.5 rounded-full bg-slate-800 text-slate-500 hover:bg-red-900/50 hover:text-red-300 transition-all opacity-0 group-hover:opacity-100 focus:opacity-100"
        aria-label="Delete pair"
      >
        <Trash2 className="w-4 h-4" />
      </button>
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-cyan-400 mb-1">Question</label>
          <textarea
            value={pair.question}
            onChange={handleQuestionChange}
            rows={2}
            className="w-full bg-slate-800 border border-slate-600 rounded-md p-2 focus:ring-1 focus:ring-cyan-500 focus:outline-none text-slate-200 resize-y transition-colors"
            placeholder="Enter a question..."
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-amber-400 mb-1">Answer</label>
           <textarea
            value={pair.answer}
            onChange={handleAnswerChange}
            rows={3}
            className="w-full bg-slate-800 border border-slate-600 rounded-md p-2 focus:ring-1 focus:ring-amber-500 focus:outline-none text-slate-200 resize-y transition-colors"
            placeholder="Enter the answer..."
          />
        </div>
      </div>
    </div>
  );
};

export default QaCard;
