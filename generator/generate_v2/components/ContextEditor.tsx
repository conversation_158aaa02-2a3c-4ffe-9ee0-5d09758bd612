import React, { useRef, useState, useEffect } from 'react';
import Button from './Button';
import Spinner from './Spinner';
import { Sparkles, AlertTriangle, XCircle, FileText, UploadCloud } from 'lucide-react';
import type { GenerationConfig } from '../types';

interface ContextEditorProps {
  context: string;
  setContext: (context: string) => void;
  largeFiles: File[] | null;
  setLargeFiles: (files: File[] | null) => void;
  onGenerate: () => void;
  isLoading: boolean;
  generationProgress: number;
  restoredFileNames: string[] | null;
  setRestoredFileNames: (names: string[] | null) => void;
  generationConfig: GenerationConfig;
  setGenerationConfig: (config: GenerationConfig) => void;
}

const PREVIEW_CHAR_LIMIT = 2000;
const LARGE_FILE_THRESHOLD = 50 * 1024 * 1024; // 50MB
const MAX_UPLOAD_SIZE = 1 * 1024 * 1024 * 1024; // 1 GB

const SettingSelect: React.FC<React.SelectHTMLAttributes<HTMLSelectElement> & { label: string }> = ({ label, id, children, ...props }) => (
  <div>
    <label htmlFor={id} className="block text-sm font-medium text-slate-300">{label}</label>
    <select
      id={id}
      {...props}
      className="mt-1 block w-full bg-slate-700/80 border-slate-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-cyan-500 focus:border-cyan-500 sm:text-sm text-white"
    >
      {children}
    </select>
  </div>
);

const ContextEditor: React.FC<ContextEditorProps> = ({ 
  context, setContext, onGenerate, isLoading, generationProgress, 
  largeFiles, setLargeFiles, restoredFileNames, setRestoredFileNames,
  generationConfig, setGenerationConfig
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [previewText, setPreviewText] = useState('');

  useEffect(() => {
    if (largeFiles && largeFiles.length > 0) {
      const firstFile = largeFiles[0];
      const slice = firstFile.slice(0, PREVIEW_CHAR_LIMIT);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewText(reader.result as string);
      };
      reader.readAsText(slice);
    }
  }, [largeFiles]);

  const isLargeFileMode = largeFiles !== null;

  const handleFileUploadClick = () => {
    setUploadError(null);
    setRestoredFileNames(null);
    fileInputRef.current?.click();
  };
  
  const handleClearContext = () => {
    setContext('');
    setLargeFiles(null);
    setPreviewText('');
    setUploadError(null);
    setRestoredFileNames(null);
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploadError(null);
    const totalSize = Array.from(files).reduce((acc, file) => acc + file.size, 0);

    if (totalSize > MAX_UPLOAD_SIZE) {
        const sizeInGB = (totalSize / (1024 * 1024 * 1024)).toFixed(2);
        setUploadError(`Total file size (${sizeInGB}GB) exceeds the 1GB limit.`);
        if (fileInputRef.current) fileInputRef.current.value = "";
        return;
    }
    
    setRestoredFileNames(null);
    setIsUploading(true);
    setUploadProgress(0);

    try {
      if (totalSize > LARGE_FILE_THRESHOLD) {
        setContext('');
        setLargeFiles(Array.from(files));
        setUploadProgress(100);
      } else {
        setLargeFiles(null);
        let bytesRead = 0;
        const fileContents: string[] = [];
        
        for (const file of Array.from(files)) {
            const content = await file.text();
            bytesRead += file.size;
            setUploadProgress(Math.round((bytesRead / totalSize) * 100));
            fileContents.push(content);
        }
        setContext(fileContents.join('\n\n---\n\n'));
      }
    } catch (err) {
      console.error("Error reading files:", err);
      const msg = err instanceof Error ? err.message : 'An unknown error occurred.';
      setUploadError(`Error reading files: ${msg}`);
    } finally {
      setIsUploading(false);
      if(fileInputRef.current) fileInputRef.current.value = "";
    }
  };
  
  const displayedContext = isLargeFileMode ? previewText : (context.length > PREVIEW_CHAR_LIMIT ? context.substring(0, PREVIEW_CHAR_LIMIT) : context);
  const showPreviewWarning = isLargeFileMode || context.length > PREVIEW_CHAR_LIMIT;

  return (
    <div className="flex flex-col h-full bg-slate-800/50 rounded-xl border border-slate-700 shadow-2xl">
       <input
        type="file" ref={fileInputRef} onChange={handleFileChange}
        multiple accept=".txt,.md,.json" className="hidden" disabled={isLoading || isUploading}
      />
      <div className="flex justify-between items-center p-4 border-b border-slate-700">
        <div>
            <h2 className="text-xl font-bold text-white">Input Context</h2>
            <p className="text-sm text-slate-400">Paste text, or upload file(s) up to 1GB.</p>
        </div>
         <Button onClick={handleFileUploadClick} variant="secondary" disabled={isLoading || isUploading}>
            <UploadCloud className="w-4 h-4 mr-2" />
            Upload File(s)
        </Button>
      </div>
      <div className="flex-grow p-4 flex flex-col space-y-4">
         {uploadError && (
             <div className="p-3 bg-red-900/50 border border-red-700 text-red-200 rounded-lg flex items-center gap-3">
                <AlertTriangle className="w-5 h-5" />
                <span>{uploadError}</span>
            </div>
        )}
        {restoredFileNames && (
          <div className="p-3 bg-blue-900/50 border border-blue-700 text-blue-200 rounded-lg flex items-center gap-3">
            <AlertTriangle className="w-5 h-5" />
            <span>Session restored. Please re-upload the following file(s) to continue: <span className="font-semibold">{restoredFileNames.join(', ')}</span></span>
          </div>
        )}
        
        {/* Generation Settings */}
        <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700 space-y-4">
          <h3 className="text-lg font-semibold text-white">Generation Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <SettingSelect label="Samples to Generate" id="samplesToGenerate" value={generationConfig.samplesToGenerate} onChange={e => setGenerationConfig({...generationConfig, samplesToGenerate: Number(e.target.value)})}>
              {[10, 50, 100, 250, 500, 1000].map(n => <option key={n} value={n}>{n}</option>)}
            </SettingSelect>
            <SettingSelect label="Sample Type" id="sampleType" value={generationConfig.sampleType} onChange={e => setGenerationConfig({...generationConfig, sampleType: e.target.value})}>
              {['Fact-based Q&A', 'Hypothetical Scenarios', 'Summarization', 'Comparative Analysis'].map(n => <option key={n} value={n}>{n}</option>)}
            </SettingSelect>
             <SettingSelect label="Persona" id="persona" value={generationConfig.persona} onChange={e => setGenerationConfig({...generationConfig, persona: e.target.value})}>
              {['Neutral', 'Expert Teacher', 'Curious Student', 'Skeptical Analyst', 'Helpful Assistant'].map(n => <option key={n} value={n}>{n}</option>)}
            </SettingSelect>
             <SettingSelect label="Tone" id="tone" value={generationConfig.tone} onChange={e => setGenerationConfig({...generationConfig, tone: e.target.value})}>
              {['Neutral', 'Formal', 'Casual', 'Humorous', 'Serious', 'Empathetic'].map(n => <option key={n} value={n}>{n}</option>)}
            </SettingSelect>
            <div className="md:col-span-2 lg:col-span-3">
              <label htmlFor="additionalInstructions" className="block text-sm font-medium text-slate-300">Additional Instructions</label>
              <textarea
                id="additionalInstructions"
                value={generationConfig.additionalInstructions}
                onChange={e => setGenerationConfig({...generationConfig, additionalInstructions: e.target.value})}
                rows={2}
                placeholder="e.g., focus on dates and names, create questions a 5th grader could answer"
                className="mt-1 block w-full bg-slate-700/80 border-slate-600 rounded-md shadow-sm p-2 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-cyan-500 sm:text-sm text-white placeholder-slate-500"
              />
            </div>
          </div>
        </div>

        {isLargeFileMode && largeFiles && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-slate-300">Large file(s) loaded for streaming:</p>
            <ul className="space-y-1">
              {largeFiles.map(file => (
                <li key={file.name} className="flex items-center gap-2 text-sm bg-slate-700/50 px-2 py-1 rounded-md">
                  <FileText className="w-4 h-4 text-slate-400" />
                  <span className="font-mono text-slate-300">{file.name}</span>
                  <span className="text-slate-500">({(file.size / (1024*1024)).toFixed(2)} MB)</span>
                </li>
              ))}
            </ul>
          </div>
        )}
        {showPreviewWarning && (
          <div className="p-3 bg-slate-700/50 border border-slate-600 text-slate-300 rounded-lg flex items-center justify-between gap-3">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-400 flex-shrink-0" />
              <p className="text-sm">
                {isLargeFileMode ? 'Showing preview of the first file.' : `Showing preview of first ${PREVIEW_CHAR_LIMIT.toLocaleString()} chars.`}
                <br />
                Full content will be used for generation.
              </p>
            </div>
            <button onClick={handleClearContext} className="p-1.5 rounded-full text-slate-400 hover:bg-slate-600 hover:text-slate-100 transition-colors flex-shrink-0" aria-label="Clear context" title="Clear context">
              <XCircle className="w-5 h-5" />
            </button>
          </div>
        )}
        <textarea
          value={displayedContext}
          onChange={(e) => isLargeFileMode || setContext(e.target.value)}
          readOnly={isLargeFileMode}
          placeholder="Paste your context here, or upload files. The more detailed the context, the better the generated Q&A pairs will be."
          className={`w-full flex-grow min-h-[200px] lg:min-h-0 bg-slate-900 border border-slate-600 rounded-md p-3 focus:ring-2 focus:ring-cyan-500 focus:outline-none resize-y text-slate-200 placeholder-slate-500 transition-colors ${isLargeFileMode ? 'cursor-default bg-slate-900/70' : ''}`}
          disabled={isLoading || isUploading}
        />
      </div>
      <div className="p-4 border-t border-slate-700">
         {(isUploading || isLoading) && (
            <div className="mb-3">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-slate-300">
                  {isUploading ? 'Reading files...' : 'Generating pairs...'}
                </span>
                <span className="text-sm font-medium text-slate-300">
                  {isUploading ? uploadProgress : generationProgress}%
                </span>
              </div>
              <div className="w-full bg-slate-700 rounded-full h-2.5">
                <div 
                  className="bg-cyan-500 h-2.5 rounded-full transition-all duration-300"
                  style={{ width: `${isUploading ? uploadProgress : generationProgress}%` }}>
                </div>
              </div>
            </div>
         )}
        <Button 
          onClick={onGenerate}
          disabled={isLoading || isUploading || (!context.trim() && !largeFiles && !restoredFileNames)}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Spinner />
              <span>Uploading...</span>
            </>
          ) : isLoading ? (
            <>
              <Spinner />
              <span>Generating...</span>
            </>
          ) : (
            <>
              <Sparkles className="w-4 h-4 mr-2" />
              <span>Generate {generationConfig.samplesToGenerate} Q&A Pairs</span>
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ContextEditor;
