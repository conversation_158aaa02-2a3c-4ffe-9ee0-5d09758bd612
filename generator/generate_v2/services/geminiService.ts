import { GoogleGenAI, Type } from "@google/genai";
import type { QAPair, GenerationConfig } from '../types';

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

const qaSchema = {
  type: Type.ARRAY,
  items: {
    type: Type.OBJECT,
    properties: {
      question: {
        type: Type.STRING,
        description: "A question that can be answered from the provided context.",
      },
      answer: {
        type: Type.STRING,
        description: "The answer to the question, extracted verbatim from the context.",
      },
    },
    required: ["question", "answer"],
  },
};

export const generateQaPairs = async (
    context: string, 
    existingQuestions: string[] = [],
    config: GenerationConfig
): Promise<QAPair[]> => {
  if (!context.trim()) {
    return [];
  }

  let systemPrompt = `You are a data generation specialist for AI training. Your task is to generate 10 high-quality and diverse question-and-answer pairs based *only* on the provided context. The answers must be concise and taken directly from the text provided. Do not invent information.`;

  if (config.persona && config.persona !== 'Neutral') {
    systemPrompt += `\n- Adopt the persona of a ${config.persona}.`;
  }
  if (config.tone && config.tone !== 'Neutral') {
    systemPrompt += `\n- The tone of the questions should be ${config.tone}.`;
  }
  if (config.sampleType && config.sampleType !== 'Fact-based Q&A') {
    systemPrompt += `\n- The question type should focus on ${config.sampleType}.`;
  }
  if (config.additionalInstructions) {
    systemPrompt += `\n- Follow these additional instructions carefully: ${config.additionalInstructions}`;
  }
  
  let userPrompt = '';

  if (existingQuestions.length > 0) {
    userPrompt += `To ensure diversity, AVOID generating questions that are substantively similar to the following examples that have already been created:\n- ${existingQuestions.join('\n- ')}\n\n`;
  }

  userPrompt += `Context:\n---\n${context}\n---`;

  try {
    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: userPrompt,
      config: {
        systemInstruction: systemPrompt,
        responseMimeType: "application/json",
        responseSchema: qaSchema,
        temperature: 0.7,
      },
    });

    const jsonText = response.text.trim();
    if (!jsonText) {
        console.warn("Gemini API returned an empty response, which can happen if it cannot find more unique pairs.");
        return [];
    }
    
    let parsedData: any;
    try {
        parsedData = JSON.parse(jsonText);
    } catch(e) {
        console.error("Failed to parse JSON from model response:", jsonText);
        return [];
    }

    if (!Array.isArray(parsedData)) {
      console.error("The model's response was not in the expected array format.");
      return [];
    }

    return parsedData.map((pair: Omit<QAPair, 'id'>, index: number) => {
        if (typeof pair.question !== 'string' || typeof pair.answer !== 'string' || !pair.question || !pair.answer) {
            console.warn('Skipping invalid or empty pair from model:', pair);
            return null;
        }
        return {
          ...pair,
          id: `qa-${Date.now()}-${index}`
        };
    }).filter((p): p is QAPair => p !== null);

  } catch (error) {
    console.error("Error generating Q&A pairs with Gemini:", error);
    throw new Error("Failed to generate Q&A pairs from the API. Please check the console for more details.");
  }
};
