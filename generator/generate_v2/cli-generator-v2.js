#!/usr/bin/env node

/**
 * Enhanced CLI Version of Conversational Training Data Generator v2
 * 
 * Now supports all the advanced generation settings from the UI:
 * - Sample count control (100, 500, 1000, 1500, 2000, custom)
 * - Sample types (Fact-based Q&A, Conversational, Technical, Educational, Creative)
 * - Personas (Neutral, Ex<PERSON>, Be<PERSON>ner, Professional, Academic)
 * - Tones (Neutral, Formal, Casual, Technical, Explanatory)
 * - Additional instructions for fine-tuned control
 * 
 * Perfect for LayerMatrix training data augmentation with precision control!
 * 
 * Usage:
 *   node cli-generator-v2.js input.txt output.json --count 1000 --type technical --persona expert --tone explanatory
 *   node cli-generator-v2.js data.json training.json --layermatrix --type conversational --persona professional
 *   node cli-generator-v2.js research.md qa.json --count 500 --instructions "Focus on practical applications"
 */

import { GoogleGenAI, Type } from "@google/genai";
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load API key from environment or .env/.env.local file
let API_KEY = process.env.API_KEY;
if (!API_KEY) {
    const envFiles = ['.env.local', '.env'];
    
    for (const envFile of envFiles) {
        try {
            const envContent = await fs.readFile(path.join(__dirname, envFile), 'utf-8');
            const match = envContent.match(/API_KEY=(.+)/);
            if (match) {
                API_KEY = match[1].trim();
                console.log(`✅ API key loaded from ${envFile}`);
                break;
            }
        } catch (e) {
            // File doesn't exist, continue to next
        }
    }
}

if (!API_KEY) {
    console.error('❌ Error: API_KEY not found in environment variables or .env/.env.local file');
    console.error('   Please set API_KEY environment variable or create .env.local file with API_KEY=your_key');
    process.exit(1);
}

const ai = new GoogleGenAI({ apiKey: API_KEY });

const qaSchema = {
    type: Type.ARRAY,
    items: {
        type: Type.OBJECT,
        properties: {
            question: {
                type: Type.STRING,
                description: "A question that can be answered from the provided context.",
            },
            answer: {
                type: Type.STRING,
                description: "The answer to the question, extracted verbatim from the context.",
            },
        },
        required: ["question", "answer"],
    },
};

// Generation configuration options
const SAMPLE_TYPES = [
    'Fact-based Q&A',
    'Conversational',
    'Technical',
    'Educational', 
    'Creative'
];

const PERSONAS = [
    'Neutral',
    'Expert',
    'Beginner',
    'Professional',
    'Academic'
];

const TONES = [
    'Neutral',
    'Formal',
    'Casual',
    'Technical',
    'Explanatory'
];

class EnhancedCLIGenerator {
    constructor() {
        this.totalGenerated = 0;
        this.targetCount = 1000;
        this.batchSize = 10; // Generate 10 Q&A pairs per API call
        
        // Enhanced generation configuration
        this.config = {
            samplesToGenerate: 1000,
            sampleType: 'Fact-based Q&A',
            persona: 'Neutral',
            tone: 'Neutral',
            additionalInstructions: ''
        };
    }

    async readFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            const ext = path.extname(filePath).toLowerCase();
            
            if (ext === '.json') {
                try {
                    const jsonData = JSON.parse(content);
                    return this.extractTextFromJson(jsonData);
                } catch (e) {
                    return content;
                }
            }
            
            return content;
        } catch (error) {
            throw new Error(`Failed to read file ${filePath}: ${error.message}`);
        }
    }

    extractTextFromJson(obj, depth = 0) {
        if (depth > 10) return '';
        
        let text = '';
        
        if (typeof obj === 'string') {
            return obj + ' ';
        } else if (Array.isArray(obj)) {
            for (const item of obj) {
                text += this.extractTextFromJson(item, depth + 1);
            }
        } else if (typeof obj === 'object' && obj !== null) {
            for (const [key, value] of Object.entries(obj)) {
                if (!['id', 'timestamp', 'created_at', 'updated_at', 'metadata'].includes(key.toLowerCase())) {
                    text += this.extractTextFromJson(value, depth + 1);
                }
            }
        }
        
        return text;
    }

    chunkText(text, maxChunkSize = 8000) {
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';
        
        for (const sentence of sentences) {
            if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
                chunks.push(currentChunk.trim());
                currentChunk = sentence;
            } else {
                currentChunk += sentence + '. ';
            }
        }
        
        if (currentChunk.trim().length > 0) {
            chunks.push(currentChunk.trim());
        }
        
        return chunks;
    }

    async generateQaPairs(context, existingQuestions = []) {
        if (!context.trim()) {
            return [];
        }

        // Build enhanced system prompt with v2 features
        let systemPrompt = `You are a data generation specialist for AI training. Your task is to generate ${this.batchSize} high-quality and diverse question-and-answer pairs based *only* on the provided context. The answers must be concise and taken directly from the text provided. Do not invent information.`;

        // Add persona guidance
        if (this.config.persona && this.config.persona !== 'Neutral') {
            systemPrompt += `\n- Adopt the persona of a ${this.config.persona}.`;
        }

        // Add tone guidance
        if (this.config.tone && this.config.tone !== 'Neutral') {
            systemPrompt += `\n- The tone of the questions should be ${this.config.tone}.`;
        }

        // Add sample type guidance
        if (this.config.sampleType && this.config.sampleType !== 'Fact-based Q&A') {
            systemPrompt += `\n- The question type should focus on ${this.config.sampleType}.`;
        }

        // Add additional instructions
        if (this.config.additionalInstructions) {
            systemPrompt += `\n- Follow these additional instructions carefully: ${this.config.additionalInstructions}`;
        }

        let userPrompt = '';

        if (existingQuestions.length > 0) {
            const recentQuestions = existingQuestions.slice(-50);
            userPrompt += `To ensure diversity, AVOID generating questions that are substantively similar to these recent examples:\n- ${recentQuestions.join('\n- ')}\n\n`;
        }

        userPrompt += `Context:\n---\n${context}\n---`;

        try {
            const response = await ai.models.generateContent({
                model: "gemini-2.5-flash",
                contents: userPrompt,
                config: {
                    systemInstruction: systemPrompt,
                    responseMimeType: "application/json",
                    responseSchema: qaSchema,
                    temperature: 0.7,
                },
            });

            const jsonText = response.text.trim();
            if (!jsonText) {
                console.warn("⚠️  API returned empty response");
                return [];
            }

            let parsedData;
            try {
                parsedData = JSON.parse(jsonText);
            } catch (e) {
                console.error("❌ Failed to parse JSON response");
                return [];
            }

            if (!Array.isArray(parsedData)) {
                console.error("❌ Response not in expected array format");
                return [];
            }

            return parsedData.map((pair, index) => {
                if (typeof pair.question !== 'string' || typeof pair.answer !== 'string' || 
                    !pair.question || !pair.answer) {
                    return null;
                }
                return {
                    id: `qa-${Date.now()}-${index}`,
                    question: pair.question,
                    answer: pair.answer
                };
            }).filter(p => p !== null);

        } catch (error) {
            console.error("❌ API Error:", error.message);
            return [];
        }
    }

    async generateFromFile(inputPath, outputPath, format = 'standard') {
        console.log(`🚀 Starting Enhanced Q&A generation from: ${inputPath}`);
        console.log(`📊 Target: ${this.targetCount} Q&A pairs`);
        console.log(`🎯 Configuration:`);
        console.log(`   Sample Type: ${this.config.sampleType}`);
        console.log(`   Persona: ${this.config.persona}`);
        console.log(`   Tone: ${this.config.tone}`);
        if (this.config.additionalInstructions) {
            console.log(`   Instructions: ${this.config.additionalInstructions}`);
        }
        
        const content = await this.readFile(inputPath);
        console.log(`📄 File content: ${content.length} characters`);
        
        const chunks = this.chunkText(content);
        console.log(`🔄 Split into ${chunks.length} chunks for processing`);
        
        const allQaPairs = [];
        const existingQuestions = [];
        
        let chunkIndex = 0;
        while (allQaPairs.length < this.targetCount && chunkIndex < chunks.length * 3) {
            const chunk = chunks[chunkIndex % chunks.length];
            
            console.log(`\n⚡ Processing chunk ${(chunkIndex % chunks.length) + 1}/${chunks.length} (Attempt ${Math.floor(chunkIndex / chunks.length) + 1})`);
            console.log(`📈 Progress: ${allQaPairs.length}/${this.targetCount} Q&A pairs generated`);
            
            try {
                const newPairs = await this.generateQaPairs(chunk, existingQuestions);
                
                if (newPairs.length > 0) {
                    allQaPairs.push(...newPairs);
                    existingQuestions.push(...newPairs.map(p => p.question));
                    console.log(`   ✅ Generated ${newPairs.length} new pairs`);
                } else {
                    console.log(`   ⚠️  No new pairs generated from this chunk`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.error(`   ❌ Error processing chunk: ${error.message}`);
            }
            
            chunkIndex++;
        }
        
        const finalPairs = allQaPairs.slice(0, this.targetCount);
        
        let outputData;
        if (format === 'layermatrix') {
            outputData = this.formatForLayerMatrix(finalPairs, inputPath);
        } else {
            outputData = {
                metadata: {
                    source_file: path.basename(inputPath),
                    generated_at: new Date().toISOString(),
                    total_pairs: finalPairs.length,
                    generator_version: "2.0.0",
                    generation_config: this.config
                },
                qa_pairs: finalPairs
            };
        }
        
        await fs.writeFile(outputPath, JSON.stringify(outputData, null, 2));
        
        console.log(`\n🎉 Enhanced generation complete!`);
        console.log(`📊 Generated: ${finalPairs.length} Q&A pairs`);
        console.log(`💾 Saved to: ${outputPath}`);
        console.log(`📈 Success rate: ${((finalPairs.length / this.targetCount) * 100).toFixed(1)}%`);
        
        return finalPairs;
    }

    formatForLayerMatrix(qaPairs, sourcePath) {
        const domain = this.inferDomain(sourcePath);
        
        return {
            metadata: {
                source_file: path.basename(sourcePath),
                domain: domain,
                generated_at: new Date().toISOString(),
                total_samples: qaPairs.length,
                format: "layermatrix_training_v2",
                generation_config: this.config,
                features: qaPairs.length > 0 ? Object.keys(qaPairs[0]).length : 0
            },
            training_data: qaPairs.map((pair, index) => ({
                id: pair.id,
                input_text: pair.question,
                target_text: pair.answer,
                domain: domain,
                sample_index: index,
                generation_config: this.config,
                metadata: {
                    source: "generated_v2",
                    quality: "high",
                    filtered: false,
                    sample_type: this.config.sampleType,
                    persona: this.config.persona,
                    tone: this.config.tone
                }
            }))
        };
    }

    inferDomain(filePath) {
        const filename = path.basename(filePath).toLowerCase();
        
        if (filename.includes('psychology') || filename.includes('emotion')) return 'psychology';
        if (filename.includes('finance') || filename.includes('economic')) return 'finance';
        if (filename.includes('ai') || filename.includes('artificial')) return 'ai_optimization';
        if (filename.includes('medical') || filename.includes('health')) return 'medical';
        if (filename.includes('legal') || filename.includes('law')) return 'international_law';
        if (filename.includes('philosophy')) return 'philosophy';
        if (filename.includes('software') || filename.includes('code')) return 'software_engineering';
        
        return 'general_knowledge';
    }

    showUsage() {
        console.log(`
🧠 Enhanced Conversational Training Data Generator CLI v2

Usage:
  node cli-generator-v2.js <input_file> <output_file> [options]

Options:
  --layermatrix              Format output for LayerMatrix training
  --count <number>           Number of Q&A pairs to generate (default: 1000)
  --type <sample_type>       Sample type: ${SAMPLE_TYPES.join(', ')}
  --persona <persona>        Persona: ${PERSONAS.join(', ')}
  --tone <tone>              Tone: ${TONES.join(', ')}
  --instructions <text>      Additional instructions for generation
  --help                     Show this help message

Examples:
  node cli-generator-v2.js document.txt qa_pairs.json --count 500 --type conversational
  node cli-generator-v2.js data.json training.json --layermatrix --persona expert --tone technical
  node cli-generator-v2.js research.md qa.json --type educational --instructions "Focus on practical applications"

Perfect for LayerMatrix training with precision control! 🚀
        `);
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.length < 2) {
        new EnhancedCLIGenerator().showUsage();
        process.exit(0);
    }
    
    const inputPath = args[0];
    const outputPath = args[1];
    const isLayerMatrix = args.includes('--layermatrix');
    
    const generator = new EnhancedCLIGenerator();
    
    // Parse enhanced options
    const countIndex = args.indexOf('--count');
    if (countIndex !== -1 && args[countIndex + 1]) {
        generator.targetCount = parseInt(args[countIndex + 1]);
        generator.config.samplesToGenerate = generator.targetCount;
    }
    
    const typeIndex = args.indexOf('--type');
    if (typeIndex !== -1 && args[typeIndex + 1]) {
        const type = args[typeIndex + 1];
        if (SAMPLE_TYPES.some(t => t.toLowerCase() === type.toLowerCase())) {
            generator.config.sampleType = SAMPLE_TYPES.find(t => t.toLowerCase() === type.toLowerCase());
        }
    }
    
    const personaIndex = args.indexOf('--persona');
    if (personaIndex !== -1 && args[personaIndex + 1]) {
        const persona = args[personaIndex + 1];
        if (PERSONAS.some(p => p.toLowerCase() === persona.toLowerCase())) {
            generator.config.persona = PERSONAS.find(p => p.toLowerCase() === persona.toLowerCase());
        }
    }
    
    const toneIndex = args.indexOf('--tone');
    if (toneIndex !== -1 && args[toneIndex + 1]) {
        const tone = args[toneIndex + 1];
        if (TONES.some(t => t.toLowerCase() === tone.toLowerCase())) {
            generator.config.tone = TONES.find(t => t.toLowerCase() === tone.toLowerCase());
        }
    }
    
    const instructionsIndex = args.indexOf('--instructions');
    if (instructionsIndex !== -1 && args[instructionsIndex + 1]) {
        generator.config.additionalInstructions = args[instructionsIndex + 1];
    }
    
    try {
        await generator.generateFromFile(
            inputPath, 
            outputPath, 
            isLayerMatrix ? 'layermatrix' : 'standard'
        );
        
        console.log(`\n✅ Ready for enhanced LayerMatrix training!`);
        
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        process.exit(1);
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
