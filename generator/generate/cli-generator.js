#!/usr/bin/env node

/**
 * CLI Version of Conversational Training Data Generator
 * 
 * Converts any file (JSON, MD, TXT) into 1000 high-quality Q&A pairs
 * Perfect for LayerMatrix training data augmentation
 * 
 * Usage:
 *   node cli-generator.js input.txt output.json
 *   node cli-generator.js --batch folder/ output_folder/
 *   node cli-generator.js --layermatrix input.json (outputs LayerMatrix format)
 */

import { GoogleGenAI, Type } from "@google/genai";
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load API key from environment or .env/.env.local file
let API_KEY = process.env.API_KEY;
if (!API_KEY) {
    // Try .env.local first, then .env
    const envFiles = ['.env.local', '.env'];

    for (const envFile of envFiles) {
        try {
            const envContent = await fs.readFile(path.join(__dirname, envFile), 'utf-8');
            const match = envContent.match(/API_KEY=(.+)/);
            if (match) {
                API_KEY = match[1].trim();
                console.log(`✅ API key loaded from ${envFile}`);
                break;
            }
        } catch (e) {
            // File doesn't exist, continue to next
        }
    }
}

if (!API_KEY) {
    console.error('❌ Error: API_KEY not found in environment variables or .env file');
    console.error('   Please set API_KEY environment variable or create .env file with API_KEY=your_key');
    process.exit(1);
}

const ai = new GoogleGenAI({ apiKey: API_KEY });

const qaSchema = {
    type: Type.ARRAY,
    items: {
        type: Type.OBJECT,
        properties: {
            question: {
                type: Type.STRING,
                description: "A question that can be answered from the provided context.",
            },
            answer: {
                type: Type.STRING,
                description: "The answer to the question, extracted verbatim from the context.",
            },
        },
        required: ["question", "answer"],
    },
};

class CLIGenerator {
    constructor() {
        this.totalGenerated = 0;
        this.targetCount = 1000;
        this.batchSize = 10; // Generate 10 Q&A pairs per API call
    }

    async readFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            const ext = path.extname(filePath).toLowerCase();
            
            if (ext === '.json') {
                // Try to extract text content from JSON
                try {
                    const jsonData = JSON.parse(content);
                    return this.extractTextFromJson(jsonData);
                } catch (e) {
                    // If JSON parsing fails, treat as plain text
                    return content;
                }
            }
            
            return content;
        } catch (error) {
            throw new Error(`Failed to read file ${filePath}: ${error.message}`);
        }
    }

    extractTextFromJson(obj, depth = 0) {
        if (depth > 10) return ''; // Prevent infinite recursion
        
        let text = '';
        
        if (typeof obj === 'string') {
            return obj + ' ';
        } else if (Array.isArray(obj)) {
            for (const item of obj) {
                text += this.extractTextFromJson(item, depth + 1);
            }
        } else if (typeof obj === 'object' && obj !== null) {
            for (const [key, value] of Object.entries(obj)) {
                // Skip certain keys that are likely not content
                if (!['id', 'timestamp', 'created_at', 'updated_at', 'metadata'].includes(key.toLowerCase())) {
                    text += this.extractTextFromJson(value, depth + 1);
                }
            }
        }
        
        return text;
    }

    chunkText(text, maxChunkSize = 8000) {
        // Split text into chunks that fit within API limits
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';
        
        for (const sentence of sentences) {
            if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
                chunks.push(currentChunk.trim());
                currentChunk = sentence;
            } else {
                currentChunk += sentence + '. ';
            }
        }
        
        if (currentChunk.trim().length > 0) {
            chunks.push(currentChunk.trim());
        }
        
        return chunks;
    }

    async generateQaPairs(context, existingQuestions = []) {
        if (!context.trim()) {
            return [];
        }

        let prompt = `You are a data generation specialist for AI training. Based *only* on the following context, generate ${this.batchSize} high-quality and diverse question-and-answer pairs. The questions should be natural and conversational. The answers must be concise and taken directly from the text provided. Do not invent information.`;

        if (existingQuestions.length > 0) {
            const recentQuestions = existingQuestions.slice(-50); // Only use recent questions to avoid prompt bloat
            prompt += `\n\nTo ensure diversity, AVOID generating questions that are substantively similar to these recent examples:\n- ${recentQuestions.join('\n- ')}`;
        }

        prompt += `\n\nContext:\n---\n${context}\n---`;

        try {
            const response = await ai.models.generateContent({
                model: "gemini-2.5-flash",
                contents: prompt,
                config: {
                    responseMimeType: "application/json",
                    responseSchema: qaSchema,
                    temperature: 0.7,
                },
            });

            const jsonText = response.text.trim();
            if (!jsonText) {
                console.warn("⚠️  API returned empty response");
                return [];
            }

            let parsedData;
            try {
                parsedData = JSON.parse(jsonText);
            } catch (e) {
                console.error("❌ Failed to parse JSON response");
                return [];
            }

            if (!Array.isArray(parsedData)) {
                console.error("❌ Response not in expected array format");
                return [];
            }

            return parsedData.map((pair, index) => {
                if (typeof pair.question !== 'string' || typeof pair.answer !== 'string' || 
                    !pair.question || !pair.answer) {
                    return null;
                }
                return {
                    id: `qa-${Date.now()}-${index}`,
                    question: pair.question,
                    answer: pair.answer
                };
            }).filter(p => p !== null);

        } catch (error) {
            console.error("❌ API Error:", error.message);
            return [];
        }
    }

    async generateFromFile(inputPath, outputPath, format = 'standard') {
        console.log(`🚀 Starting Q&A generation from: ${inputPath}`);
        console.log(`📊 Target: ${this.targetCount} Q&A pairs`);
        
        // Read and process input file
        const content = await this.readFile(inputPath);
        console.log(`📄 File content: ${content.length} characters`);
        
        // Split into chunks
        const chunks = this.chunkText(content);
        console.log(`🔄 Split into ${chunks.length} chunks for processing`);
        
        const allQaPairs = [];
        const existingQuestions = [];
        
        // Process chunks to reach target count
        let chunkIndex = 0;
        while (allQaPairs.length < this.targetCount && chunkIndex < chunks.length * 3) {
            const chunk = chunks[chunkIndex % chunks.length];
            
            console.log(`\n⚡ Processing chunk ${(chunkIndex % chunks.length) + 1}/${chunks.length} (Attempt ${Math.floor(chunkIndex / chunks.length) + 1})`);
            console.log(`📈 Progress: ${allQaPairs.length}/${this.targetCount} Q&A pairs generated`);
            
            try {
                const newPairs = await this.generateQaPairs(chunk, existingQuestions);
                
                if (newPairs.length > 0) {
                    allQaPairs.push(...newPairs);
                    existingQuestions.push(...newPairs.map(p => p.question));
                    console.log(`   ✅ Generated ${newPairs.length} new pairs`);
                } else {
                    console.log(`   ⚠️  No new pairs generated from this chunk`);
                }
                
                // Small delay to be respectful to API
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                console.error(`   ❌ Error processing chunk: ${error.message}`);
            }
            
            chunkIndex++;
        }
        
        // Trim to exact target count
        const finalPairs = allQaPairs.slice(0, this.targetCount);
        
        // Format output based on requested format
        let outputData;
        if (format === 'layermatrix') {
            outputData = this.formatForLayerMatrix(finalPairs, inputPath);
        } else {
            outputData = {
                metadata: {
                    source_file: path.basename(inputPath),
                    generated_at: new Date().toISOString(),
                    total_pairs: finalPairs.length,
                    generator_version: "1.0.0"
                },
                qa_pairs: finalPairs
            };
        }
        
        // Save output
        await fs.writeFile(outputPath, JSON.stringify(outputData, null, 2));
        
        console.log(`\n🎉 Generation complete!`);
        console.log(`📊 Generated: ${finalPairs.length} Q&A pairs`);
        console.log(`💾 Saved to: ${outputPath}`);
        console.log(`📈 Success rate: ${((finalPairs.length / this.targetCount) * 100).toFixed(1)}%`);
        
        return finalPairs;
    }

    formatForLayerMatrix(qaPairs, sourcePath) {
        // Format specifically for LayerMatrix training
        const domain = this.inferDomain(sourcePath);
        
        return {
            metadata: {
                source_file: path.basename(sourcePath),
                domain: domain,
                generated_at: new Date().toISOString(),
                total_samples: qaPairs.length,
                format: "layermatrix_training",
                features: qaPairs.length > 0 ? Object.keys(qaPairs[0]).length : 0
            },
            training_data: qaPairs.map((pair, index) => ({
                id: pair.id,
                input_text: pair.question,
                target_text: pair.answer,
                domain: domain,
                sample_index: index,
                metadata: {
                    source: "generated",
                    quality: "high",
                    filtered: false
                }
            }))
        };
    }

    inferDomain(filePath) {
        const filename = path.basename(filePath).toLowerCase();
        
        if (filename.includes('psychology') || filename.includes('emotion')) return 'psychology';
        if (filename.includes('finance') || filename.includes('economic')) return 'finance';
        if (filename.includes('ai') || filename.includes('artificial')) return 'ai_optimization';
        if (filename.includes('medical') || filename.includes('health')) return 'medical';
        if (filename.includes('legal') || filename.includes('law')) return 'international_law';
        if (filename.includes('philosophy')) return 'philosophy';
        if (filename.includes('software') || filename.includes('code')) return 'software_engineering';
        
        return 'general_knowledge';
    }

    showUsage() {
        console.log(`
🧠 Conversational Training Data Generator CLI

Usage:
  node cli-generator.js <input_file> <output_file> [options]

Options:
  --layermatrix     Format output for LayerMatrix training
  --count <number>  Number of Q&A pairs to generate (default: 1000)
  --help           Show this help message

Examples:
  node cli-generator.js document.txt qa_pairs.json
  node cli-generator.js data.json training_data.json --layermatrix
  node cli-generator.js research.md qa_1000.json --count 1000

Supported input formats: .txt, .md, .json
Output format: JSON with Q&A pairs ready for training
        `);
    }
}

// CLI Interface
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.length < 2) {
        new CLIGenerator().showUsage();
        process.exit(0);
    }
    
    const inputPath = args[0];
    const outputPath = args[1];
    const isLayerMatrix = args.includes('--layermatrix');
    
    const countIndex = args.indexOf('--count');
    const targetCount = countIndex !== -1 && args[countIndex + 1] ? 
        parseInt(args[countIndex + 1]) : 1000;
    
    try {
        const generator = new CLIGenerator();
        generator.targetCount = targetCount;
        
        await generator.generateFromFile(
            inputPath, 
            outputPath, 
            isLayerMatrix ? 'layermatrix' : 'standard'
        );
        
        console.log(`\n✅ Ready for LayerMatrix training!`);
        
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        process.exit(1);
    }
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}
