/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M18.518 17.347A7 7 0 0 1 14 19", key: "1emhpo" }],
  ["path", { d: "M18.8 4A11 11 0 0 1 20 9", key: "127b67" }],
  ["path", { d: "M9 9h.01", key: "1q5me6" }],
  ["circle", { cx: "20", cy: "16", r: "2", key: "1v9bxh" }],
  ["circle", { cx: "9", cy: "9", r: "7", key: "p2h5vp" }],
  ["rect", { x: "4", y: "16", width: "10", height: "6", rx: "2", key: "bfnviv" }]
];
const BellElectric = createLucideIcon("bell-electric", __iconNode);

export { __iconNode, BellElectric as default };
//# sourceMappingURL=bell-electric.js.map
