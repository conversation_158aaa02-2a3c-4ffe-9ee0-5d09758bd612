/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5", key: "x6cv4u" }],
  ["path", { d: "M18 12h.01", key: "yjnet6" }],
  ["path", { d: "M19 22v-6", key: "qhmiwi" }],
  ["path", { d: "m22 19-3-3-3 3", key: "rn6bg2" }],
  ["path", { d: "M6 12h.01", key: "c2rlol" }],
  ["circle", { cx: "12", cy: "12", r: "2", key: "1c9p78" }]
];
const BanknoteArrowUp = createLucideIcon("banknote-arrow-up", __iconNode);

export { __iconNode, BanknoteArrowUp as default };
//# sourceMappingURL=banknote-arrow-up.js.map
