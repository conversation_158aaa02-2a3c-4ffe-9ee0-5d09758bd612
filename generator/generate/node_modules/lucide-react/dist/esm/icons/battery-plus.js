/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 9v6", key: "17i7lo" }],
  ["path", { d: "M12.543 6H16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.605", key: "o09yah" }],
  ["path", { d: "M22 14v-4", key: "14q9d5" }],
  ["path", { d: "M7 12h6", key: "iekk3h" }],
  ["path", { d: "M7.606 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.606", key: "xyqvf1" }]
];
const BatteryPlus = createLucideIcon("battery-plus", __iconNode);

export { __iconNode, BatteryPlus as default };
//# sourceMappingURL=battery-plus.js.map
