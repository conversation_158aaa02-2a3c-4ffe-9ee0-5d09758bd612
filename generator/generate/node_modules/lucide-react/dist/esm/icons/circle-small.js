/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [["circle", { cx: "12", cy: "12", r: "6", key: "1vlfrh" }]];
const CircleSmall = createLucideIcon("circle-small", __iconNode);

export { __iconNode, CircleSmall as default };
//# sourceMappingURL=circle-small.js.map
