/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 3a41 41 0 0 0 0 18", key: "1qcnzb" }],
  ["path", { d: "M14 3a41 41 0 0 1 0 18", key: "547vd4" }],
  [
    "path",
    {
      d: "M17 3a2 2 0 0 1 1.68.92 15.25 15.25 0 0 1 0 16.16A2 2 0 0 1 17 21H7a2 2 0 0 1-1.68-.92 15.25 15.25 0 0 1 0-16.16A2 2 0 0 1 7 3z",
      key: "1wepyy"
    }
  ],
  ["path", { d: "M3.84 17h16.32", key: "1wh981" }],
  ["path", { d: "M3.84 7h16.32", key: "19jf4x" }]
];
const Barrel = createLucideIcon("barrel", __iconNode);

export { __iconNode, Barrel as default };
//# sourceMappingURL=barrel.js.map
