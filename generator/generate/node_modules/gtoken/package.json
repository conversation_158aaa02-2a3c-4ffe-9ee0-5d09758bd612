{"name": "gtoken", "version": "7.1.0", "description": "Node.js Google Authentication Service Account Tokens", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "engines": {"node": ">=14.0.0"}, "repository": "google/node-gtoken", "scripts": {"lint": "gts check", "clean": "gts clean", "fix": "gts fix", "compile": "tsc -p .", "test": "c8 mocha build/test", "prepare": "npm run compile", "pretest": "npm run compile", "presystem-test": "npm run compile", "system-test": "mocha build/system-test", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "docs": "compodoc src/", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean"}, "keywords": ["google", "service", "account", "api", "token", "api", "auth"], "author": {"name": "Google, LLC"}, "license": "MIT", "dependencies": {"gaxios": "^6.0.0", "jws": "^4.0.0"}, "devDependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@compodoc/compodoc": "^1.1.7", "@types/jws": "^3.1.0", "@types/mocha": "^9.0.0", "@types/node": "^20.0.0", "c8": "^9.0.0", "gts": "^5.0.0", "linkinator": "^4.0.0", "mocha": "^9.2.2", "nock": "^13.0.0", "typescript": "^5.1.6"}, "files": ["build/src", "!build/src/**/*.map"]}