../../../bin/evaluate-cli,sha256=jr21Xe_nwb9DBsG_X9-8xjEdA9jTeBFyhJLGQPs0RhY,266
evaluate-0.4.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
evaluate-0.4.5.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
evaluate-0.4.5.dist-info/METADATA,sha256=czq9HNbPJ-cWEMNUnPgySh5v815I1plnh4rQDhXmXeQ,9521
evaluate-0.4.5.dist-info/RECORD,,
evaluate-0.4.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
evaluate-0.4.5.dist-info/WHEEL,sha256=tZoeGjtWxWRfdplE7E3d45VPlLNQnvbKiYnx7gwAy8A,92
evaluate-0.4.5.dist-info/entry_points.txt,sha256=m2P3heof0lsg47nq6tYW_yUtxTfimd3RuD26Yk8KMkM,70
evaluate-0.4.5.dist-info/top_level.txt,sha256=wBEoxird-u8p4OKDwq5z9rlfH-ybeez8rjaKNLNJ3B0,9
evaluate/__init__.py,sha256=pASJAEDB0v9_quEpedIkfFCVzsyl1igrbcm3Mw0Nk6Y,1754
evaluate/__pycache__/__init__.cpython-311.pyc,,
evaluate/__pycache__/config.cpython-311.pyc,,
evaluate/__pycache__/hub.cpython-311.pyc,,
evaluate/__pycache__/info.cpython-311.pyc,,
evaluate/__pycache__/inspect.cpython-311.pyc,,
evaluate/__pycache__/loading.cpython-311.pyc,,
evaluate/__pycache__/module.cpython-311.pyc,,
evaluate/__pycache__/naming.cpython-311.pyc,,
evaluate/__pycache__/saving.cpython-311.pyc,,
evaluate/__pycache__/visualization.cpython-311.pyc,,
evaluate/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
evaluate/commands/__pycache__/__init__.cpython-311.pyc,,
evaluate/commands/__pycache__/evaluate_cli.cpython-311.pyc,,
evaluate/commands/evaluate_cli.py,sha256=w7GWb48JPjoC0BX7Jn12qtxQUBYOlZNhdg4YegA93Fw,4491
evaluate/config.py,sha256=g4g-S6hVAw0Ys9As7gKaFP66pZeh8hoJJ5GEXaLSWV8,6648
evaluate/evaluation_suite/__init__.py,sha256=TjcFihBDf_ZQAoIjSXPEC0iFBeEC_LFqCfXKbrkyhWs,4941
evaluate/evaluation_suite/__pycache__/__init__.cpython-311.pyc,,
evaluate/evaluator/__init__.py,sha256=JoWqRP-qCgNzDre6nO8zpJ2Iyp0eUkN7eDKPOPUXz2g,5788
evaluate/evaluator/__pycache__/__init__.cpython-311.pyc,,
evaluate/evaluator/__pycache__/audio_classification.cpython-311.pyc,,
evaluate/evaluator/__pycache__/automatic_speech_recognition.cpython-311.pyc,,
evaluate/evaluator/__pycache__/base.cpython-311.pyc,,
evaluate/evaluator/__pycache__/image_classification.cpython-311.pyc,,
evaluate/evaluator/__pycache__/question_answering.cpython-311.pyc,,
evaluate/evaluator/__pycache__/text2text_generation.cpython-311.pyc,,
evaluate/evaluator/__pycache__/text_classification.cpython-311.pyc,,
evaluate/evaluator/__pycache__/text_generation.cpython-311.pyc,,
evaluate/evaluator/__pycache__/token_classification.cpython-311.pyc,,
evaluate/evaluator/__pycache__/utils.cpython-311.pyc,,
evaluate/evaluator/audio_classification.py,sha256=v5myOnm0PN8BWVnm4nWCzcyklaLtdnbOS3EJ09TPFhg,5804
evaluate/evaluator/automatic_speech_recognition.py,sha256=jOveYJXsH-t5SzGe7FzXhnHeDKFhqWZUtK3S1l9XYus,4392
evaluate/evaluator/base.py,sha256=--M302w8Bea6u6iYCc9dGFZL1wDIRGd7uUorhcmUAus,22881
evaluate/evaluator/image_classification.py,sha256=RJ7NUS91hjZkr5JqhqtYsr5dxBkChA3Qim6An8fHT50,4751
evaluate/evaluator/question_answering.py,sha256=ArF5BKfE9J9uC-q1GQwbvkAHw1ThgA997ERKmPS-Z4g,9566
evaluate/evaluator/text2text_generation.py,sha256=M2itKYfIz9z_9J-Y7sXyx4HKMhQbdYwbv8oThSw8Yzw,9676
evaluate/evaluator/text_classification.py,sha256=g1MUwa3TCUCUBGvZDmdeJ_l8BAOgbn0Q0y4TDvep8Uk,6676
evaluate/evaluator/text_generation.py,sha256=4ZnHweTUpvNZhaprewTPms__00I8Tnje586ZDCG_ZlU,2679
evaluate/evaluator/token_classification.py,sha256=XMzteW1coN2e3KWmpWj-OGafj22pzMa7UiHylooirHk,11546
evaluate/evaluator/utils.py,sha256=HDKdLWLHtfpP-Hhe9cf1TFVIRsmfNgLHifDcGYujKZs,2451
evaluate/hub.py,sha256=ZX6VYZU0EkjTWmABuJ6Zg6oHXIT2dHkHy0u8RgyL9UQ,4550
evaluate/info.py,sha256=l5gXfqHhj77-XvFhz57Mns-Ev-lNJsLxsyYPHPvSzj0,5490
evaluate/inspect.py,sha256=vVSCLr7HWLxIpXzwpDPuiE5XwiP5QQ82oGkdok7aO7o,4969
evaluate/loading.py,sha256=P5MjZvrGHRgOE6jVPnyCNWOpbY-iPz_kLIydZjiNT7Q,35219
evaluate/module.py,sha256=vMsLOskdsD6c_pU85AVo_kceg_r1RNGMFGAR6oZZuHM,46420
evaluate/naming.py,sha256=Lpw8JmoJfiWs4xDUMEDzcIKO9Nw9RS2lzjeuUP-9acA,2827
evaluate/saving.py,sha256=UoixNIHmWEceJREvGZlJNViVjRkgNf3MRflwnnhnNUA,2159
evaluate/utils/__init__.py,sha256=kdFi2pVFSXm_y4EvvuQNnlPUkOPmGLNtc9YTfxAmdsI,1201
evaluate/utils/__pycache__/__init__.cpython-311.pyc,,
evaluate/utils/__pycache__/file_utils.cpython-311.pyc,,
evaluate/utils/__pycache__/gradio.cpython-311.pyc,,
evaluate/utils/__pycache__/logging.cpython-311.pyc,,
evaluate/utils/file_utils.py,sha256=4jtbBhFfAjrHOIEwFcXaZ5H1bw-gCfVPTzfW5BE36Rk,22144
evaluate/utils/gradio.py,sha256=UXGRxiPsJ41Xm5gGF7Jf_1vTOPopE_wDoBIyBS0S8d4,4434
evaluate/utils/logging.py,sha256=nRy963i3_-H0Qcer6ETgnTFiJoQhojSiapeXQ9-eUyk,6698
evaluate/visualization.py,sha256=m-mD6vxOIQ-_KXTues2tB4r7c4jdygBybHJeidP-jgw,9293
